package com.haoxue.mcpserver

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import com.haoxue.libcommon.utils.Toaster
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class McpServerService : Service() {
    enum class ServerStatus {
        STOPPED, STARTING, RUNNING
    }

    private val binder = LocalBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    private var mcpServerManager: McpServerManager? = null

    // 服务器状态
    private val _serverStatus = MutableStateFlow(ServerStatus.STOPPED)

    inner class LocalBinder : Binder() {
        fun getService(): McpServerService = this@McpServerService
    }

    override fun onBind(intent: Intent?): IBinder {
        return binder
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    fun startServer(port: Int, serverName: String, authToken: String = "") {
        if (_serverStatus.value != ServerStatus.STOPPED) {
            addLog("服务器已在运行或正在启动中")
            return
        }
        serviceScope.launch {
            try {
                _serverStatus.value = ServerStatus.STARTING
                addLog("正在启动 MCP 服务器...")
                addLog("端口: $port")
                addLog("服务器名称: $serverName")

                // 显示鉴权状态
                if (authToken.isNotEmpty()) {
                    addLog("🔐 鉴权已启用")
                    addLog("Auth Token: $authToken")
                } else {
                    addLog("🔓 鉴权已禁用")
                }

                // 确保之前的服务器实例已清理
                stopServerInternal()

                // 创建并启动 MCP 服务器
                mcpServerManager = McpServerManager(
                    port = port,
                    serverName = serverName,
                    authToken = authToken,
                )
                mcpServerManager?.start()

                _serverStatus.value = ServerStatus.RUNNING

                Toaster.toast("MCP 服务已启动")

                addLog("MCP 服务器启动成功!")
                addLog("SSE 端点: http://localhost:$port/sse")

                if (authToken.isNotEmpty()) {
                    addLog("")
                    addLog("🔐 鉴权信息:")
                    addLog("连接时需要在 HTTP 头部包含以下任一项:")
                    addLog("- token $authToken")
                    addLog("")
                    addLog("示例连接:")
                    addLog("curl -H 'Authorization: Bearer $authToken' http://localhost:$port/sse")
                }
            } catch (e: Exception) {
                _serverStatus.value = ServerStatus.STOPPED
                addLog("启动服务器失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    fun stopServer() {
        if (_serverStatus.value == ServerStatus.STOPPED) {
            addLog("服务器已经停止")
            return
        }

        serviceScope.launch {
            stopServerInternal()
        }
    }

    private suspend fun stopServerInternal() {
        try {
            addLog("正在停止 MCP 服务器...")

            // 停止 MCP 服务器
            mcpServerManager?.stop()
            mcpServerManager = null

            _serverStatus.value = ServerStatus.STOPPED
            addLog("MCP 服务器已停止")

            stopForeground(STOP_FOREGROUND_REMOVE)

        } catch (e: Exception) {
            addLog("停止服务器时出错: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun addLog(message: String) {
        Logcat.d(message)
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope.launch {
            stopServer()
        }
        serviceScope.cancel()
        addLog("MCP 服务已销毁")
    }
}
