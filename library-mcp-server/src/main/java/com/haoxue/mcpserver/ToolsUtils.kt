package com.haoxue.mcpserver

import com.haoxue.libcommon.router.RouterPaths
import com.haoxue.libcommon.router.RouterUtils
import com.haoxue.libcommon.utils.AppManager

object ToolsUtils {

    private const val localIntercept = true

    fun interceptFun(text: String): String? {
        if (!localIntercept) return null
        try {
            text.contains()
            when (text) {
                "开始跳绳",
                "打开跳绳",
                "启动跳绳",
                "我想跳绳",
                "开始跳绳运动",
                "我要跳绳" -> {
                    RouterUtils.navigation(RouterPaths.Pose.DETECTION)
                    return "好的，打开跳绳"
                }

                "结束跳绳",
                "关闭跳绳",
                "退出跳绳",
                "跳绳结束",
                "停止跳绳" -> {
                    AppManager.killTopActivity()
                    return "好的，关闭跳绳"
                }

                else -> {
                    return null
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
}