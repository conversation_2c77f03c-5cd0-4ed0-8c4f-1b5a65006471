package com.haoxue.mcpserver

import com.haoxue.libcommon.router.RouterPaths
import com.haoxue.libcommon.router.RouterUtils
import com.haoxue.libcommon.utils.AppManager

object ToolsUtils {

    private const val localIntercept = true

    /**
     * 本地指令拦截器
     * 支持模糊匹配和关键词检测
     */
    fun interceptFun(text: String): String? {
        if (!localIntercept) return null

        try {
            val normalizedText = text.trim().lowercase()

            // 跳绳启动指令 - 使用关键词匹配
            if (containsJumpRopeStartKeywords(normalizedText)) {
                if (AppManager.topActivity == "PoseActivity") {
                    return "⚠️ 抱歉，当前正在跳绳训练中，请勿重复操作！"
                }
                RouterUtils.navigation(RouterPaths.Pose.DETECTION)
                return "🏃‍♀️ 好的，正在为您打开AI跳绳训练界面！\n\n请确保：\n• 摄像头能清楚看到您的全身\n• 周围有足够的运动空间\n• 光线充足便于AI识别\n\n祝您运动愉快！💪"
            }

            // 跳绳结束指令 - 使用关键词匹配
            if (containsJumpRopeStopKeywords(normalizedText)) {
                if (AppManager.topActivity == "McpActivity") {
                    return "⚠️ 抱歉，当前没有跳绳训练进行中，请勿重复操作！"
                }
                AppManager.killTopActivity()
                return "🏁 好的，跳绳训练已结束！\n\n✅ 运动数据已保存\n📊 感谢您的坚持训练！\n💡 记得适当休息，保持健康的运动习惯。"
            }

            return null

        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 检查是否包含跳绳启动关键词
     */
    private fun containsJumpRopeStartKeywords(text: String): Boolean {
        val startKeywords = listOf(
            // 直接指令
            "开始跳绳", "打开跳绳", "启动跳绳", "跳绳开始",
            // 意愿表达
            "我想跳绳", "我要跳绳", "想要跳绳", "要跳绳",
            // 运动相关
            "开始跳绳运动", "跳绳训练", "跳绳锻炼", "跳绳健身",
            // 请求式
            "帮我打开跳绳", "请打开跳绳", "可以跳绳吗"
        )

        return startKeywords.any { keyword ->
            text.contains(keyword.lowercase())
        }
    }

    /**
     * 检查是否包含跳绳结束关键词
     */
    private fun containsJumpRopeStopKeywords(text: String): Boolean {
        val stopKeywords = listOf(
            // 直接指令
            "结束跳绳", "关闭跳绳", "退出跳绳", "停止跳绳",
            // 状态描述
            "跳绳结束", "跳绳完了", "不跳了", "跳完了",
            // 请求式
            "帮我关闭跳绳", "请关闭跳绳", "可以结束了"
        )

        return stopKeywords.any { keyword ->
            text.contains(keyword.lowercase())
        }
    }
}