package com.haoxue.libcommon.router

/**
 * ARouter 路由路径常量
 * 统一管理所有页面路由路径
 */
object RouterPaths {

    /**
     * App 模块路由路径
     */
    object App {
        private const val APP = "/app"
        const val MAIN = "$APP/main"           // 主页面
        const val MCP = "$APP/mcp"             // MCP 页面
    }

    /**
     * Pose 模块路由路径
     */
    object Pose {
        private const val POSE = "/pose"
        const val DETECTION = "$POSE/detection"   // 姿态检测页面
    }

    /**
     * Common 模块路由路径
     */
    object Common {
        private const val COMMON = "/common"
    }

}
