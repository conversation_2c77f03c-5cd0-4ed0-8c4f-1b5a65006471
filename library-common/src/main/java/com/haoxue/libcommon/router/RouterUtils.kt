package com.haoxue.libcommon.router

import android.app.Activity
import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.launcher.ARouter
import com.lazy.library.logging.Logcat

/**
 * ARouter 路由工具类
 * 提供便捷的页面跳转方法
 */
object RouterUtils {

    /**
     * 简单跳转
     * @param path 路由路径
     */
    fun navigation(path: String) {
        ARouter.getInstance().build(path).navigation()
    }

    /**
     * 带参数跳转
     * @param path 路由路径
     * @param params 参数 Map
     */
    fun navigation(path: String, params: Map<String, Any>) {
        val postcard = ARouter.getInstance().build(path)
        params.forEach { (key, value) ->
            when (value) {
                is String -> postcard.withString(key, value)
                is Int -> postcard.withInt(key, value)
                is Long -> postcard.withLong(key, value)
                is Boolean -> postcard.withBoolean(key, value)
                is Float -> postcard.withFloat(key, value)
                is Double -> postcard.withDouble(key, value)
                else -> postcard.withObject(key, value)
            }
        }
        postcard.navigation()
    }

    /**
     * 带回调的跳转
     * @param context 上下文
     * @param path 路由路径
     * @param requestCode 请求码
     */
    fun navigationForResult(context: Activity, path: String, requestCode: Int) {
        ARouter.getInstance().build(path).navigation(context, requestCode)
    }

    /**
     * 带参数和回调的跳转
     */
    fun navigationForResult(
        context: Activity,
        path: String,
        requestCode: Int,
        params: Map<String, Any>
    ) {
        val postcard = ARouter.getInstance().build(path)
        params.forEach { (key, value) ->
            when (value) {
                is String -> postcard.withString(key, value)
                is Int -> postcard.withInt(key, value)
                is Long -> postcard.withLong(key, value)
                is Boolean -> postcard.withBoolean(key, value)
                is Float -> postcard.withFloat(key, value)
                is Double -> postcard.withDouble(key, value)
                else -> postcard.withObject(key, value)
            }
        }
        postcard.navigation(context, requestCode)
    }

    /**
     * 获取 Fragment
     * @param path 路由路径
     * @return Fragment 实例
     */
    fun getFragment(path: String): Fragment? {
        return try {
            ARouter.getInstance().build(path).navigation() as? Fragment
        } catch (e: Exception) {
            Logcat.e("获取 Fragment 失败: $path", e)
            null
        }
    }

    /**
     * 带参数获取 Fragment
     */
    fun getFragment(path: String, params: Map<String, Any>): Fragment? {
        return try {
            val postcard = ARouter.getInstance().build(path)
            params.forEach { (key, value) ->
                when (value) {
                    is String -> postcard.withString(key, value)
                    is Int -> postcard.withInt(key, value)
                    is Long -> postcard.withLong(key, value)
                    is Boolean -> postcard.withBoolean(key, value)
                    is Float -> postcard.withFloat(key, value)
                    is Double -> postcard.withDouble(key, value)
                    else -> postcard.withObject(key, value)
                }
            }
            postcard.navigation() as? Fragment
        } catch (e: Exception) {
            Logcat.e("获取 Fragment 失败: $path", e)
            null
        }
    }

    /**
     * 检查路由是否存在
     * @param path 路由路径
     * @return 是否存在
     */
    fun isRouteExist(path: String): Boolean {
        return try {
            val postcard = ARouter.getInstance().build(path)
            postcard.getType() != null
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 构建 Postcard（用于复杂配置）
     * @param path 路由路径
     * @return Postcard 实例
     */
    fun build(path: String): Postcard {
        return ARouter.getInstance().build(path)
    }

}
