package com.haoxue.libcommon

/**
 * app内url管理
 */
interface Urls {

    interface BaseUrl {
        companion object {

            const val URL_DEV = "http://xxxx/"
            const val URL_GATEWAY = "https://xxxx/"

            private const val URL_H5_DEV = ""
            private const val URL_H5_RELEASE = ""


            val COMMON_URL = when {
                !BuildConfig.IS_DEVELOP_ENV -> URL_GATEWAY
                else -> URL_DEV
            }

            val COMMON_URL_H5 = when {
                !BuildConfig.IS_DEVELOP_ENV -> URL_H5_RELEASE
                else -> URL_H5_DEV
            }
        }
    }


    interface Path {
        companion object {
        }
    }

    interface ThirdPath {
        companion object {

        }
    }
}
