package com.haoxue.libcommon.markdown

import android.content.Context
import android.widget.TextView
import io.noties.markwon.Markwon
import com.lazy.library.logging.Logcat

/**
 * 最基础的 Markdown 渲染器
 * 只使用 Markwon 核心功能，避免所有依赖问题
 */
object BasicMarkdownRenderer {
    
    private var markwon: Markwon? = null
    private var isInitialized = false
    
    /**
     * 初始化基础版 Markwon
     */
    fun initialize(context: Context) {
        if (isInitialized) return
        
        try {
            // 使用最简单的 Markwon 配置
            markwon = Markwon.create(context)
            isInitialized = true
//            Logcat.d("基础版 Markwon 初始化成功")
            
        } catch (e: Exception) {
            Logcat.e("基础版 Markwon 初始化失败", e)
        }
    }
    
    /**
     * 渲染 Markdown 到 TextView
     */
    fun render(textView: TextView, markdown: String) {
        try {
            if (!isInitialized) {
                initialize(textView.context)
            }
            
            markwon?.setMarkdown(textView, markdown)
            
        } catch (e: Exception) {
            Logcat.e("基础版 Markdown 渲染失败", e)
            // 降级处理：直接显示原始文本
            textView.text = markdown
        }
    }
    
    /**
     * 流式渲染 Markdown
     */
    fun renderStreaming(
        textView: TextView, 
        markdownBuilder: StringBuilder, 
        newContent: String
    ) {
        try {
            markdownBuilder.append(newContent)
            render(textView, markdownBuilder.toString())
            
            // 自动滚动到底部
            textView.post {
                val scrollView = findScrollView(textView)
                scrollView?.fullScroll(android.view.View.FOCUS_DOWN)
            }
            
        } catch (e: Exception) {
            Logcat.e("基础版流式 Markdown 渲染失败", e)
            // 降级处理
            markdownBuilder.append(newContent)
            textView.text = markdownBuilder.toString()
        }
    }
    
    /**
     * 清空内容
     */
    fun clear(textView: TextView, markdownBuilder: StringBuilder) {
        try {
            markdownBuilder.clear()
            textView.text = ""
        } catch (e: Exception) {
            Logcat.e("清空 Markdown 内容失败", e)
        }
    }
    
    /**
     * 查找父级 ScrollView
     */
    private fun findScrollView(view: android.view.View): android.widget.ScrollView? {
        var parent = view.parent
        while (parent != null) {
            if (parent is android.widget.ScrollView) {
                return parent
            }
            parent = parent.parent
        }
        return null
    }
    
    /**
     * 获取 Markwon 实例
     */
    fun getMarkwon(context: Context): Markwon? {
        if (!isInitialized) {
            initialize(context)
        }
        return markwon
    }
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
}

/**
 * 基础版 Markdown 工具类
 */
object BasicMarkdownUtils {
    
    /**
     * 快速渲染 Markdown
     */
    fun render(textView: TextView, markdown: String) {
        BasicMarkdownRenderer.render(textView, markdown)
    }
    
    /**
     * 流式渲染
     */
    fun renderStreaming(
        textView: TextView,
        markdownBuilder: StringBuilder,
        newContent: String
    ) {
        BasicMarkdownRenderer.renderStreaming(textView, markdownBuilder, newContent)
    }
    
    /**
     * 清空内容
     */
    fun clear(textView: TextView, markdownBuilder: StringBuilder) {
        BasicMarkdownRenderer.clear(textView, markdownBuilder)
    }
    
    /**
     * 检查文本是否包含 Markdown 语法
     */
    fun isMarkdown(text: String): Boolean {
        return try {
            text.contains("```") ||      // 代码块
                   text.contains("**") ||       // 粗体
                   text.contains("*") ||        // 斜体
                   text.contains("#") ||        // 标题
                   text.contains("[") ||        // 链接
                   text.contains("|") ||        // 表格
                   text.contains(">") ||        // 引用
                   text.contains("-") ||        // 列表
                   text.contains("1.")          // 有序列表
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 格式化代码块
     */
    fun formatCodeBlock(code: String, language: String = ""): String {
        return "```$language\n$code\n```"
    }
    
    /**
     * 格式化表格
     */
    fun formatTable(headers: List<String>, rows: List<List<String>>): String {
        return try {
            val sb = StringBuilder()
            
            // 表头
            sb.append("| ${headers.joinToString(" | ")} |\n")
            
            // 分隔线
            sb.append("| ${headers.joinToString(" | ") { "---" }} |\n")
            
            // 数据行
            rows.forEach { row ->
                sb.append("| ${row.joinToString(" | ")} |\n")
            }
            
            sb.toString()
        } catch (e: Exception) {
            "表格格式化失败: ${e.message}"
        }
    }
}
