package com.haoxue.libcommon.markdown

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.haoxue.libcommon.R
import com.lazy.library.logging.Logcat

/**
 * 基础聊天适配器
 * 使用最简单的 Markwon 配置，避免依赖问题
 */
class BasicChatAdapter(
    private val context: Context,
    private val onItemClick: ((ChatMessage, Int) -> Unit)? = null
) : RecyclerView.Adapter<BasicChatAdapter.ChatViewHolder>() {
    
    private val chatMessages = mutableListOf<ChatMessage>()
    
    /**
     * 聊天消息数据类
     */
    data class ChatMessage(
        val type: Int, // 0: 用户, 1: AI
        val content: String,
        val isMarkdown: Boolean = false,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * ViewHolder
     */
    class ChatViewHolder(
        val leftTextView: TextView,
        val rightTextView: TextView
    ) : RecyclerView.ViewHolder(leftTextView.parent as ViewGroup)
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = inflater.inflate(R.layout.chat_item_markwon, parent, false)
        
        val leftTextView = view.findViewById<TextView>(R.id.tv_content_left)
        val rightTextView = view.findViewById<TextView>(R.id.tv_content_right)
        
        return ChatViewHolder(leftTextView, rightTextView)
    }
    
    override fun onBindViewHolder(holder: ChatViewHolder, position: Int) {
        val message = chatMessages[position]
        
        // 根据消息类型选择对应的 TextView
        val (targetTextView, hideTextView) = if (message.type == 0) {
            // 用户消息 - 右侧
            holder.rightTextView.visibility = android.view.View.VISIBLE
            holder.leftTextView.visibility = android.view.View.GONE
            Pair(holder.rightTextView, holder.leftTextView)
        } else {
            // AI 消息 - 左侧
            holder.leftTextView.visibility = android.view.View.VISIBLE
            holder.rightTextView.visibility = android.view.View.GONE
            Pair(holder.leftTextView, holder.rightTextView)
        }
        
        // 渲染内容
        try {
            if (message.isMarkdown) {
                BasicMarkdownRenderer.render(targetTextView, message.content)
            } else {
                targetTextView.text = message.content
            }
        } catch (e: Exception) {
            Logcat.e("渲染消息失败", e)
            targetTextView.text = message.content
        }
        
        // 设置点击事件
        targetTextView.setOnClickListener {
            onItemClick?.invoke(message, position)
        }
    }
    
    override fun getItemCount(): Int = chatMessages.size
    
    /**
     * 添加消息
     */
    fun addMessage(message: ChatMessage) {
        try {
            chatMessages.add(message)
            notifyItemInserted(chatMessages.size - 1)
        } catch (e: Exception) {
            Logcat.e("添加消息失败", e)
        }
    }
    
    /**
     * 更新最后一条消息（用于流式更新）
     */
    fun updateLastMessage(content: String, isMarkdown: Boolean = false) {
        try {
            if (chatMessages.isNotEmpty()) {
                val lastIndex = chatMessages.size - 1
                val lastMessage = chatMessages[lastIndex]
                chatMessages[lastIndex] = lastMessage.copy(
                    content = content,
                    isMarkdown = isMarkdown
                )
                notifyItemChanged(lastIndex)
            }
        } catch (e: Exception) {
            Logcat.e("更新最后一条消息失败", e)
        }
    }
    
    /**
     * 清空所有消息
     */
    fun clearMessages() {
        try {
            val size = chatMessages.size
            chatMessages.clear()
            notifyItemRangeRemoved(0, size)
        } catch (e: Exception) {
            Logcat.e("清空消息失败", e)
        }
    }
    
    /**
     * 获取所有消息
     */
    fun getMessages(): List<ChatMessage> = chatMessages.toList()
    
    /**
     * 获取最后一条消息
     */
    fun getLastMessage(): ChatMessage? = chatMessages.lastOrNull()
}

/**
 * 基础 Markdown RecyclerView 工具类
 */
object BasicMarkdownRecyclerUtils {

    // 用于跟踪用户是否正在手动滑动
    private val userScrollingStates = mutableMapOf<RecyclerView, Boolean>()
    private val scrollListeners = mutableMapOf<RecyclerView, RecyclerView.OnScrollListener>()

    // 滚动状态回调
    private val scrollStateCallbacks = mutableMapOf<RecyclerView, (Boolean) -> Unit>()

    /**
     * 为聊天 RecyclerView 设置基础适配器
     */
    fun setupChatRecyclerView(
        recyclerView: RecyclerView,
        context: Context,
        onItemClick: ((BasicChatAdapter.ChatMessage, Int) -> Unit)? = null,
        onScrollStateChanged: ((Boolean) -> Unit)? = null
    ): BasicChatAdapter {
        try {
            val adapter = BasicChatAdapter(context, onItemClick)
            recyclerView.adapter = adapter

            // 设置布局管理器
            recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context)

            // 保存滚动状态回调
            if (onScrollStateChanged != null) {
                scrollStateCallbacks[recyclerView] = onScrollStateChanged
            }

            // 设置滚动监听器来检测用户手动滑动
            setupScrollListener(recyclerView)

            return adapter
        } catch (e: Exception) {
            Logcat.e("设置聊天 RecyclerView 失败", e)
            throw e
        }
    }

    /**
     * 设置滚动监听器
     */
    private fun setupScrollListener(recyclerView: RecyclerView) {
        try {
            // 移除之前的监听器
            scrollListeners[recyclerView]?.let { oldListener ->
                recyclerView.removeOnScrollListener(oldListener)
            }

            val scrollListener = object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)

                    when (newState) {
                        RecyclerView.SCROLL_STATE_DRAGGING -> {
                            // 用户开始手动滑动
                            userScrollingStates[recyclerView] = true
//                            Logcat.d("用户开始手动滑动 RecyclerView")
                        }
                        RecyclerView.SCROLL_STATE_IDLE -> {
                            // 滑动停止，检查是否在底部
                            val layoutManager = recyclerView.layoutManager as? androidx.recyclerview.widget.LinearLayoutManager
                            val adapter = recyclerView.adapter

                            if (layoutManager != null && adapter != null) {
                                val lastVisiblePosition = layoutManager.findLastCompletelyVisibleItemPosition()
                                val isAtBottom = lastVisiblePosition >= adapter.itemCount - 1

                                if (isAtBottom) {
                                    // 用户滑动到底部，恢复自动滚动
                                    userScrollingStates[recyclerView] = false
//                                    Logcat.d("用户滑动到底部，恢复自动滚动")
                                } else {
//                                    Logcat.d("用户不在底部，保持手动滑动状态")
                                }

                                // 通知滚动状态变化（true表示不在底部，需要显示滚动按钮）
                                scrollStateCallbacks[recyclerView]?.invoke(!isAtBottom)
                            }
                        }
                    }
                }
            }

            recyclerView.addOnScrollListener(scrollListener)
            scrollListeners[recyclerView] = scrollListener

            // 初始状态：允许自动滚动
            userScrollingStates[recyclerView] = false

        } catch (e: Exception) {
            Logcat.e("设置滚动监听器失败", e)
        }
    }

    /**
     * 智能滚动到底部 - 只在用户没有手动滑动时才自动滚动
     */
    fun scrollToBottom(recyclerView: RecyclerView) {
        try {
            val isUserScrolling = userScrollingStates[recyclerView] ?: false

            if (!isUserScrolling) {
                recyclerView.post {
                    val adapter = recyclerView.adapter
                    if (adapter != null && adapter.itemCount > 0) {
                        recyclerView.smoothScrollToPosition(adapter.itemCount - 1)
//                        Logcat.d("自动滚动到底部，消息数量: ${adapter.itemCount}")
                    }
                }
            } else {
//                Logcat.d("用户正在手动滑动，跳过自动滚动")
            }
        } catch (e: Exception) {
            Logcat.e("滚动到底部失败", e)
        }
    }

    /**
     * 强制滚动到底部 - 无论用户是否在滑动都会执行
     */
    fun forceScrollToBottom(recyclerView: RecyclerView) {
        try {
            recyclerView.post {
                val adapter = recyclerView.adapter
                if (adapter != null && adapter.itemCount > 0) {
                    recyclerView.smoothScrollToPosition(adapter.itemCount - 1)
//                    Logcat.d("强制滚动到底部，消息数量: ${adapter.itemCount}")
                }
            }
            // 重置用户滑动状态
            userScrollingStates[recyclerView] = false
        } catch (e: Exception) {
//            Logcat.e("强制滚动到底部失败", e)
        }
    }

    /**
     * 检查用户是否正在手动滑动
     */
    fun isUserScrolling(recyclerView: RecyclerView): Boolean {
        return userScrollingStates[recyclerView] ?: false
    }

    /**
     * 清理资源
     */
    fun cleanup(recyclerView: RecyclerView) {
        try {
            scrollListeners[recyclerView]?.let { listener ->
                recyclerView.removeOnScrollListener(listener)
            }
            scrollListeners.remove(recyclerView)
            userScrollingStates.remove(recyclerView)
            scrollStateCallbacks.remove(recyclerView)
            Logcat.d("清理 RecyclerView 滚动监听器")
        } catch (e: Exception) {
            Logcat.e("清理 RecyclerView 资源失败", e)
        }
    }
}
