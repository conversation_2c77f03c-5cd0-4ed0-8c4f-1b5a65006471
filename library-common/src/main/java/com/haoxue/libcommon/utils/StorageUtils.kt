package com.haoxue.libcommon.utils

import android.content.Context
import android.os.Environment
import android.os.StatFs
import java.io.File

object StorageUtils {
    @get:Throws(IllegalArgumentException::class)
    val availableExternalMemorySize: Long
        /**
         * 获取SDCARD剩余存储空间
         *
         * @return
         */
        get() {
            if (externalMemoryAvailable()) {
                val path = Environment.getExternalStorageDirectory()
                val stat = StatFs(path.getPath())
                val blockSize = stat.getBlockSize().toLong()
                val availableBlocks = stat.getAvailableBlocks().toLong()
                return availableBlocks * blockSize
            } else {
                return -1
            }
        }

    val totalExternalMemorySize: Long
        /**
         * 获取SDCARD总存储空间
         *
         * @return
         */
        get() {
            if (externalMemoryAvailable()) {
                val path = Environment.getExternalStorageDirectory()
                val stat = StatFs(path.getPath())
                val blockSize = stat.getBlockSize().toLong()
                val totalBlocks = stat.getBlockCount().toLong()
                return totalBlocks * blockSize
            } else {
                return -1
            }
        }

    /**
     * SDCARD是否存在
     */
    fun externalMemoryAvailable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }

    /**
     * 将数字大小转为“MB”、“KB”、“GB”格式
     *
     * @param size
     * @return
     */
    fun getSize(size: Long): String? {
        if (size < 0) return null

        var result: String? = null
        if (size > 1024 * 1024 * 1024) {
            val f = size.toFloat() / (1024 * 1024 * 1024)
            val s = f.toString()
            if (s.length - s.indexOf(".") > 2) result = s.substring(0, s.indexOf(".") + 3)
            else result = s
            return result + "GB"
        } else if (size > 1024 * 1024) {
            val f = size.toFloat() / (1024 * 1024)
            val s = f.toString()
            if (s.length - s.indexOf(".") > 2) result = s.substring(0, s.indexOf(".") + 3)
            else result = s
            return result + "MB"
        } else if (size > 1024) {
            val f = size.toFloat() / 1024
            val s = f.toString()
            if (s.length - s.indexOf(".") > 2) result = s.substring(0, s.indexOf(".") + 3)
            else result = s
            return result + "KB"
        } else if (size < 1024) {
            return size.toString() + "B"
        } else return null
    }


    fun getFreeSpace(dir: String?): Long {
        var state: StatFs? = null
        try {
            state = StatFs(dir)
            val blockSize = state.getBlockSize().toLong()
            val availableCount = state.getAvailableBlocks().toLong()
            val free = availableCount * blockSize
            if (free > 0) {
                return free
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0
    }


    fun getDiskCacheDir(context: Context, uniqueName: String?): File {
        val cachePath: String?
        if (Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()
            || !Environment.isExternalStorageRemovable()
        ) {
            val externalCacheDir = context.getExternalCacheDir()
            // context.getExternalCacheDir() maybe null
            if (externalCacheDir == null) {
                cachePath = context.getCacheDir().getPath()
            } else {
                cachePath = externalCacheDir.getPath()
            }
        } else {
            cachePath = context.getCacheDir().getPath()
        }
        val file = File(cachePath + File.separator + uniqueName)
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir()
        }
        return file
    }

    fun getDiskDir(uniqueName: String?): File? {
        if (Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()
            || !Environment.isExternalStorageRemovable()
        ) {
            val storageDirectory = Environment.getExternalStorageDirectory()
            if (storageDirectory != null) {
                val file = File(storageDirectory.toString() + File.separator + uniqueName)
                if (!file.exists() && !file.isDirectory()) {
                    file.mkdir()
                }
                return file
            }
        }
        return null
    }
}