package com.haoxue.libcommon.utils

import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.style.BackgroundColorSpan
import android.text.style.ClickableSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.SubscriptSpan
import android.text.style.SuperscriptSpan
import android.text.style.UnderlineSpan
import android.view.View
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.haoxue.libcommon.BaseCommonApp

class SpannableStringUtils(source: CharSequence?) : SpannableString(source) {
    /**
     * 设置字体颜色
     *
     * @param color
     * @param start
     * @param end
     * @return
     */
    fun setColor(@ColorRes color: Int, start: Int, end: Int): SpannableStringUtils {
        val span = object : UnderlineSpan() {
            override fun updateDrawState(ds: TextPaint) {
                ds.color = ContextCompat.getColor(BaseCommonApp.mContext, color)
                ds.isUnderlineText = false
            }
        }
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置字体背景色
     */
    fun setBackGround(@ColorRes color: Int, start: Int, end: Int): SpannableStringUtils {
        val span = BackgroundColorSpan(ContextCompat.getColor(BaseCommonApp.mContext, color))
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置字体大小
     */
    fun setRelativeSize(ff: Float, start: Int, end: Int): SpannableStringUtils {
        val span = RelativeSizeSpan(ff)
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置删除线
     */
    fun setStrikethroughSpan(start: Int, end: Int): SpannableStringUtils {
        val span = StrikethroughSpan()
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置下划线
     */
    fun setUnderlineSpan(start: Int, end: Int): SpannableStringUtils {
        val span = UnderlineSpan()
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置上标
     */
    fun setSuperscriptSpan(start: Int, end: Int): SpannableStringUtils {
        val span = SuperscriptSpan()
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置下标
     */
    fun setSubscriptSpan(start: Int, end: Int): SpannableStringUtils {
        val span = SubscriptSpan()
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置粗体
     */
    fun setBold(start: Int, end: Int): SpannableStringUtils {
        val span = StyleSpan(Typeface.BOLD)
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }

    /**
     * 设置斜体
     */
    fun setItalic(start: Int, end: Int): SpannableStringUtils {
        val span = StyleSpan(Typeface.ITALIC)
        setSpan(span, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
        return this
    }


    fun setOnClick(start: Int, end: Int, @ColorRes color: Int, onClickSpanListener: OnClickSpanListener): SpannableStringUtils {
        val span: ClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                onClickSpanListener.onClickSpanListener()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.color = ContextCompat.getColor(BaseCommonApp.mContext, color)
                ds.isUnderlineText = false
            }
        }
        setSpan(span, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return this
    }

    /**
     * 添加回掉接口
     */
    interface OnClickSpanListener {
        fun onClickSpanListener()
    }
}