package com.haoxue.libcommon.utils

import android.app.Application
import android.content.Context
import com.alguojian.mylibrary.StatusLayout
import com.alibaba.android.arouter.launcher.ARouter
import com.haoxue.libcommon.BuildConfig
import com.haoxue.libcommon.ConstData
import com.haoxue.libcommon.ui.widget.StatusLayoutAdapter
import com.lazy.library.logging.Logcat
import com.tencent.bugly.crashreport.CrashReport
import xyz.doikki.videoplayer.player.AndroidMediaPlayerFactory
import xyz.doikki.videoplayer.player.VideoViewConfig
import xyz.doikki.videoplayer.player.VideoViewManager


object ThirdSdk {
    /**
     * 初始化应用数据
     *
     * 该方法主要用于在应用启动时初始化一些第三方库和配置
     * 它包括日志初始化、状态布局配置、崩溃报告以及视频播放器的设置
     *
     * @param context 上下文对象，用于初始化一些需要上下文环境的库或服务
     */
    fun initData(context: Application) {

        // 初始化 ARouter
        if (BuildConfig.IS_DEBUG_MODE) {
            ARouter.openLog()     // 打印日志
            ARouter.openDebug()   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(context) // 尽可能早，推荐在Application中初始化

        // 初始化日志系统
        initLog(context)
        // 设置状态布局的默认适配器
        StatusLayout.setDefaultAdapter(StatusLayoutAdapter())
        // 根据构建配置开启或关闭调试模式
        StatusLayout.setDebug(BuildConfig.IS_DEBUG_MODE)

        // 初始化Bugly崩溃报告系统
        CrashReport.initCrashReport(context, ConstData.BUGLY_APP_ID, true)

        // 配置视频播放器
        VideoViewManager.setConfig(
            VideoViewConfig.newBuilder()
                .setPlayerFactory(AndroidMediaPlayerFactory.create())
                .build()
        )
    }

    private fun initLog(context: Context) {
        val logTag = ConstData.LOG_TAG
        val builder = Logcat.newBuilder()
        //设置Log 保存的文件夹
        builder.logSavePath(StorageUtils.getDiskCacheDir(context, "ai-sport-log"))

        //设置输出日志等级
        if (BuildConfig.DEBUG) {
            builder.logCatLogLevel(Logcat.SHOW_ALL_LOG)
            //设置输出文件日志等级
            builder.fileLogLevel(Logcat.SHOW_ALL_LOG)
        } else {
            builder.logCatLogLevel(Logcat.SHOW_INFO_LOG.code or Logcat.SHOW_WARN_LOG.code or Logcat.SHOW_ERROR_LOG.code)
            //设置输出文件日志等级
            builder.fileLogLevel(Logcat.SHOW_INFO_LOG.code or Logcat.SHOW_WARN_LOG.code or Logcat.SHOW_ERROR_LOG.code)
        }

        builder.topLevelTag(logTag)

        //删除过了几天无用日志条目
        builder.deleteUnusedLogEntriesAfterDays(30)

        //是否自动保存日志到文件中
        builder.autoSaveLogToFile(true)

        //是否显示打印日志调用堆栈信息
        builder.showStackTraceInfo(true)

        //是否显示文件日志的时间
        builder.showFileTimeInfo(true)

        //是否显示文件日志的进程以及Linux线程
        builder.showFilePidInfo(true)

        //是否显示文件日志级别
        builder.showFileLogLevel(true)

        //是否显示文件日志标签
        builder.showFileLogTag(true)

        //是否显示文件日志调用堆栈信息
        builder.showFileStackTraceInfo(true)

        //添加该标签,日志将被写入文件
        builder.addTagToFile(logTag)
        Logcat.initialize(context, builder.build())
    }
}