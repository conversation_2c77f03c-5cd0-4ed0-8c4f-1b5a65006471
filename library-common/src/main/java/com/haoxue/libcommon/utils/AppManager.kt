package com.haoxue.libcommon.utils

import android.app.Activity
import java.lang.ref.WeakReference
import java.util.Stack

/**
 * APP 栈管理所有页面
 */
object AppManager {

    var stack: Stack<WeakReference<Activity>>? = null

    /***
     * 栈中Activity的数
     *
     * @return Activity的数
     */
    fun stackSize(): Int {
        return stack!!.size
    }

    /**
     * 添加Activity到堆栈
     */
    fun addActivity(activity: WeakReference<Activity>?) {
        if (activity == null) {
            return
        }
        if (this.stack == null) {
            this.stack = Stack<WeakReference<Activity>>()
        }
        stack!!.add(activity)
    }

    /**
     * 删除ac
     *
     * @param activity 弱引用的ac
     */
    fun removeActivity(activity: WeakReference<Activity>?) {
        if (this.stack != null && activity != null) {
            stack!!.remove(activity)
        }
    }

    val topActivity: String?
        /***
         * 获取栈顶Activity（堆栈中最后一个压入的）
         * @return Activity
         */
        get() {
            if (null == this.stack || stack!!.isEmpty()) {
                return ""
            }
            val activity = stack!!.lastElement().get()
            if (null == activity) {
                return null
            } else {
                return activity.javaClass.getSimpleName()
            }
        }

    val topActivityThis: Activity?
        /***
         * 获取栈顶Activity（堆栈中最后一个压入的）
         * @return Activity
         */
        get() {
            if (null == this.stack || stack!!.isEmpty()) {
                return null
            }
            return stack!!.lastElement().get()
        }

    val secondActivity: String?
        /**
         * 获得站内第二个zctivity
         *
         * @return
         */
        get() {
            var className: String? = null
            if (this.stack != null && stack!!.size >= 2) {
                val activityWeakReference =
                    stack!!.get(stack!!.size - 2)
                className = activityWeakReference.get()!!.javaClass.getSimpleName()
            }
            return className
        }

    /***
     * 通过class 获取栈顶Activity
     * @param cls
     * @return Activity
     */
    fun getActivityByClass(cls: Class<*>?): Activity? {
        var return_activity: Activity? = null
        for (activity in this.stack!!) {
            if (activity.get()!!.javaClass == cls) {
                return_activity = activity.get()
                break
            }
        }
        return return_activity
    }

    /**
     * 结束栈顶Activity（堆栈中最后一个压入的）
     */
    fun killTopActivity() {
        try {
            val activity = stack!!.lastElement()
            killActivity(activity)
        } catch (e: Exception) {
        }
    }

    /***
     * 结束指定的Activity
     *
     * @param activity
     */
    fun killActivity(activity: WeakReference<Activity>) {
        try {
            val iterator = stack!!.iterator()
            while (iterator.hasNext()) {
                val stackActivity = iterator.next()
                if (stackActivity.get() == null) {
                    iterator.remove()
                    continue
                }
                if (stackActivity.get()!!.javaClass.getName() == activity.get()!!.javaClass.getName()) {
                    iterator.remove()
                    stackActivity.get()!!.finish()
                    break
                }
            }
        } catch (e: Exception) {
        }
    }

    /***
     * 结束指定类名的Activity
     *
     * @param cls
     */
    fun killActivity(cls: Class<*>?) {
        try {
            val listIterator = stack!!.listIterator()
            while (listIterator.hasNext()) {
                val activity = listIterator.next()!!.get()
                if (activity == null) {
                    listIterator.remove()
                    continue
                }
                if (activity.javaClass == cls) {
                    listIterator.remove()
                    if (activity != null) {
                        activity.finish()
                    }
                    break
                }
            }
        } catch (e: Exception) {
        }
    }

    /**
     * 结束所有Activity
     */
    fun killAllActivity() {
        if (this.stack == null) {
            return
        }
        try {
            val listIterator = stack!!.listIterator()
            while (listIterator.hasNext()) {
                val activity = listIterator.next()!!.get()
                if (activity != null) {
                    activity.finish()
                }
                listIterator.remove()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 移除除了某个activity的其他所有activity
     *
     * @param cls 界面
     */
    fun killAllActivityExceptOne(cls: Class<*>?) {
        if (this.stack == null) {
            return
        }
        try {
            val listIterator = stack!!.listIterator()
            while (listIterator.hasNext()) {
                val activity = listIterator.next()!!.get()
                if (activity != null && activity.javaClass == null || activity!!.javaClass != cls) {
                    activity.finish()
                    listIterator.remove()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
