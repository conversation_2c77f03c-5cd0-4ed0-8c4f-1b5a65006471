package com.haoxue.libcommon.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Service
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.text.TextUtils
import android.widget.ImageView
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.haoxue.libcommon.R
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

/**
 * glide加载图片管理类
 */
object GlideUtils {
    private var index = 0

    private val options = RequestOptions()
            .dontAnimate()
            .dontTransform()
            .placeholder(R.drawable.common_place_holder_icon)
            .error(R.drawable.common_load_error_icon)
            .fallback(R.drawable.common_load_error_icon)
            .diskCacheStrategy(DiskCacheStrategy.ALL)

    val noCachedNoPlaceholderOptions = RequestOptions()
            .error(R.drawable.common_load_error_icon)
            .fallback(R.drawable.common_load_error_icon)
            .diskCacheStrategy(DiskCacheStrategy.NONE)

    private val gifOptions = RequestOptions()
            .placeholder(R.drawable.common_place_holder_icon)
            .error(R.drawable.common_load_error_icon)
            .fallback(R.drawable.common_load_error_icon)
            .diskCacheStrategy(DiskCacheStrategy.NONE)

    private val gifOptionsNoPlaceHolder = RequestOptions()
            .error(R.drawable.common_load_error_icon)
            .fallback(R.drawable.common_load_error_icon)
            .diskCacheStrategy(DiskCacheStrategy.NONE)

    private val optionsNoPlaceHolder = RequestOptions()
            .dontAnimate()
            .dontTransform()
            .error(R.drawable.common_load_error_icon)
            .fallback(R.drawable.common_load_error_icon)
            .diskCacheStrategy(DiskCacheStrategy.ALL)


    @JvmStatic
    @JvmOverloads
    fun display(
        activity: Activity?,
        uri: Any?,
        imageView: ImageView?,
        requestOptions: RequestOptions? = options,
        asGif: Boolean = false,
        noPlaceHolder: Boolean = false
    ) {
        displayImage(activity, uri, imageView, requestOptions, asGif, noPlaceHolder)
    }

    @JvmStatic
    fun display(
            uri: Any?, imageView: ImageView?,
    ) {
        if (uri == null || (uri is String && TextUtils.isEmpty(uri)))
            return

        if (imageView == null) {
            return
        }
        val mRequestManager = Glide.with(imageView)

        var isGif = false

        if (uri is String && (uri.endsWith(".gif", true) || uri.contains(".gif?"))) {
            isGif = true
        }

        if (isGif)
            mRequestManager.asGif()

        mRequestManager
                .load(uri)
                .apply(gifOptions)
                .into(imageView)
    }

    @JvmStatic
    @JvmOverloads
    fun display(
        activity: Service?,
        uri: Any?,
        imageView: ImageView?,
        requestOptions: RequestOptions? = options,
        asGif: Boolean = false,
        noPlaceHolder: Boolean = false
    ) {
        displayImage(activity, uri, imageView, requestOptions, asGif, noPlaceHolder)
    }

    @JvmStatic
    @JvmOverloads
    fun display(
        fragment: Fragment?,
        uri: Any?,
        imageView: ImageView?,
        requestOptions: RequestOptions? = options,
        asGif: Boolean = false,
        noPlaceHolder: Boolean = false
    ) {
        if (fragment == null || fragment.activity == null) {
            return
        }
        displayImage(fragment.activity, uri, imageView, requestOptions, asGif, noPlaceHolder)
    }

    @JvmStatic
    fun clearView(context: Fragment?, imageView: ImageView?) {
        if (context == null || imageView == null) {
            return
        }
        Glide.with(imageView).clear(imageView)
    }


    @JvmStatic
    @JvmOverloads
    fun display(
        context: Context?,
        uri: Any?,
        imageView: ImageView?,
        requestOptions: RequestOptions? = options,
        asGif: Boolean = false,
        noPlaceHolder: Boolean = false
    ) {
        displayImage(context, uri, imageView, requestOptions, asGif, noPlaceHolder)
    }


    private fun displayImage(
            context: Context?,
            uri: Any?,
            imageView: ImageView?,
            requestOptions: RequestOptions?,
            asGif: Boolean,
            noPlaceHolder: Boolean
    ) {
        if (uri == null || (uri is String && TextUtils.isEmpty(uri)))
            return

        if (context == null || imageView == null) {
            return
        }
        val mRequestManager = Glide.with(context)

        var isGif = false

        if (uri is String && (uri.endsWith(".gif", true) || uri.contains(".gif?"))) {
            isGif = true
        }

        if (asGif)
            isGif = true

        if (isGif)
            mRequestManager.asGif()

        mRequestManager
                .load(uri)
                .apply(
                        when {
                            isGif && noPlaceHolder -> gifOptionsNoPlaceHolder
                            isGif -> gifOptions
                            noPlaceHolder -> optionsNoPlaceHolder
                            else -> requestOptions ?: options
                        }
                )
                .into(imageView)
    }

    /**
     * 通过加载获得bitmap
     */
    @JvmStatic
    @SuppressLint("CheckResult")
    fun getBitmap(context: Context, uri: Any, mGlideBitmapListener: GlideBitmapListener?, isOriginal: Boolean = true) {
        Glide.with(context)
                .asBitmap()
                .load(uri)
                .apply(
                        RequestOptions().dontAnimate().centerCrop()
                                .diskCacheStrategy(DiskCacheStrategy.NONE)
                )
                .into(object : SimpleTarget<Bitmap>(
                        if (isOriginal) com.bumptech.glide.request.target.Target.SIZE_ORIGINAL else 300,
                        if (isOriginal) com.bumptech.glide.request.target.Target.SIZE_ORIGINAL else 200
                ) {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        mGlideBitmapListener?.getBitmap(resource)
                    }
                })
    }

    /**
     * 通过加载获得bitmap
     */
    @JvmStatic
    @SuppressLint("CheckResult")
    fun getFie(context: Context, uri: Any, glideFileListener: GlideFileListener?) {
        getBitmap(context, uri, object : GlideBitmapListener {
            override fun getBitmap(bitmap: Bitmap) {
                val f = File(context.cacheDir.absolutePath + System.nanoTime() + ".png")
                if (f.exists()) {
                    f.delete()
                }
                try {
                    val out = FileOutputStream(f)
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
                    out.flush()
                    out.close()

                } catch (e: FileNotFoundException) {
                    e.printStackTrace()
                } catch (e: IOException) {
                    e.printStackTrace()
                } finally {
                    glideFileListener?.getFile(f)
                }
            }
        }, true)
    }


    /**
     * 通过加载获得bitmap集合
     */
    @JvmStatic
    @SuppressLint("CheckResult")
    fun getBitmapList(context: Context, urls: ArrayList<String>, mGlideBitmapsListener: GlideBitmapsListener) {
        val arrayListOf = ArrayList<Bitmap>()
        index = 0
        for (index in urls.indices) {
            val mRequestManager = Glide.with(context)
            val fileRequestBuilder = mRequestManager.asBitmap()
            fileRequestBuilder.load(urls[index]).apply(
                    RequestOptions().dontAnimate().centerCrop()
                            .diskCacheStrategy(DiskCacheStrategy.NONE)
            )
            getBitmap(fileRequestBuilder, index, urls.size, arrayListOf, mGlideBitmapsListener)
        }
    }


    private fun getBitmap(
            requestBuilder: RequestBuilder<Bitmap>,
            position: Int,
            size: Int,
            arrayList: ArrayList<Bitmap>,
            mGlideBitmapsListener: GlideBitmapsListener
    ) {
        requestBuilder
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        index++
                        arrayList.add(position, resource)
                        if (index == size) {
                            mGlideBitmapsListener.getBitmap(arrayList)
                        }
                    }
                })
    }

    /**
     * 创建群组头像
     */
    @JvmStatic
    fun createNineBitmap(size: Int, gap: Int, bitmaps: ArrayList<Bitmap>): Bitmap {
        var subSize = 0
        when {
            bitmaps.size < 2 -> subSize = size
            bitmaps.size < 5 -> subSize = (size - gap) / 2
            bitmaps.size < 10 -> subSize = (size - 2 * gap) / 3
        }

        val result = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        canvas.drawColor(Color.parseColor("#999999"))

        val count = bitmaps.size
        var subBitmap: Bitmap
        for (i in 0 until count) {
            subBitmap = Bitmap.createScaledBitmap(bitmaps[i], subSize, subSize, true)

            var x = 0f
            var y = 0f

            if (count == 2) {
                x = (i * (subSize + gap)).toFloat()
                y = (size - subSize) / 2.0f
            } else if (count == 3) {
                if (i == 0) {
                    x = (size - subSize) / 2.0f
                    y = 0f
                } else {
                    x = ((i - 1) * (subSize + gap)).toFloat()
                    y = (subSize + gap).toFloat()
                }
            } else if (count == 4) {
                x = (i % 2 * (subSize + gap)).toFloat()
                y = if (i < 2) {
                    0f
                } else {
                    (subSize + gap).toFloat()
                }
            } else if (count == 5) {
                when (i) {
                    0 -> {
                        y = (size - 2 * subSize - gap) / 2.0f
                        x = y
                    }
                    1 -> {
                        x = (size + gap) / 2.0f
                        y = (size - 2 * subSize - gap) / 2.0f
                    }
                    else -> {
                        x = ((i - 2) * (subSize + gap)).toFloat()
                        y = (size + gap) / 2.0f
                    }
                }
            } else if (count == 6) {
                x = (i % 3 * (subSize + gap)).toFloat()
                y = if (i < 3) {
                    (size - 2 * subSize - gap) / 2.0f
                } else {
                    (size + gap) / 2.0f
                }
            } else if (count == 7) {
                when {
                    i == 0 -> {
                        x = (size - subSize) / 2.0f
                        y = 0f
                    }
                    i < 4 -> {
                        x = ((i - 1) * (subSize + gap)).toFloat()
                        y = (subSize + gap).toFloat()
                    }
                    else -> {
                        x = ((i - 4) * (subSize + gap)).toFloat()
                        y = (2 * (subSize + gap)).toFloat()
                    }
                }
            } else if (count == 8) {
                when {
                    i == 0 -> {
                        x = (size - 2 * subSize - gap) / 2.0f
                        y = 0f
                    }
                    i == 1 -> {
                        x = (size + gap) / 2.0f
                        y = 0f
                    }
                    i < 5 -> {
                        x = ((i - 2) * (subSize + gap)).toFloat()
                        y = (subSize + gap).toFloat()
                    }
                    else -> {
                        x = ((i - 5) * (subSize + gap)).toFloat()
                        y = (2 * (subSize + gap)).toFloat()
                    }
                }
            } else if (count == 9) {
                x = (i % 3 * (subSize + gap)).toFloat()
                y = if (i < 3) {
                    0f
                } else if (i < 6) {
                    (subSize + gap).toFloat()
                } else {
                    (2 * (subSize + gap)).toFloat()
                }
            }
            canvas.drawBitmap(subBitmap, x, y, null)
        }
        return result
    }


    /**
     * 阿里云的缩略图
     */
    @JvmStatic
    fun getThumbUrl(path: String): String {
        return "$path?x-oss-process=image/resize,l_470"
    }

    /**
     * 阿里云的缩略图
     */
    @JvmStatic
    fun getThumbUrlTwo(path: String): String {
        return "$path?x-oss-process=image/resize,w_360"
    }


    interface GlideFileListener {
        fun getFile(file: File?)
    }

    interface GlideBitmapListener {
        fun getBitmap(bitmap: Bitmap)
    }

    interface GlideBitmapsListener {
        fun getBitmap(bitmaps: ArrayList<Bitmap>)
    }

}
