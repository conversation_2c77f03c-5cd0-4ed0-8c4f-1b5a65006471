package com.haoxue.libcommon.utils

import android.annotation.SuppressLint
import android.content.Context
import com.haoxue.libcommon.audio.AudioQueuePlayer
import com.lazy.library.logging.Logcat

/**
 * 音频播放工具类
 * 简化音频播放队列的使用
 */
object AudioPlaybackUtils {

    @SuppressLint("StaticFieldLeak")
    private var audioPlayer: AudioQueuePlayer? = null
    private var isInitialized = false

    /**
     * 初始化音频播放器
     * @param context 上下文
     */
    fun initialize(context: Context) {
        try {
            if (!isInitialized) {
                audioPlayer = AudioQueuePlayer(context.applicationContext)
                isInitialized = true
//                Logcat.d("音频播放器初始化成功")
            }
        } catch (e: Exception) {
//            Logcat.e("音频播放器初始化失败", e)
        }
    }

    /**
     * 添加音频到播放队列
     * @param audioUrl 音频文件 URL
     */
    fun playAudio(audioUrl: String) {
        try {
            if (!isInitialized) {
                Logcat.w("音频播放器未初始化")
                return
            }

            val queueSizeBefore = audioPlayer?.getQueueSize() ?: 0
            audioPlayer?.addToQueue(audioUrl)
            val queueSizeAfter = audioPlayer?.getQueueSize() ?: 0

            Logcat.d("音频已添加到播放队列: $audioUrl")
            Logcat.d("队列长度变化: $queueSizeBefore -> $queueSizeAfter")
        } catch (e: Exception) {
            Logcat.e("添加音频到播放队列失败", e)
        }
    }

    /**
     * 批量添加音频到播放队列
     * @param audioUrls 音频文件 URL 列表
     */
    fun playAudioList(audioUrls: List<String>) {
        try {
            audioUrls.forEach { url ->
                playAudio(url)
            }
            Logcat.d("批量添加音频到播放队列: ${audioUrls.size} 个")
        } catch (e: Exception) {
            Logcat.e("批量添加音频失败", e)
        }
    }

    /**
     * 暂停播放
     */
    fun pause() {
        try {
            audioPlayer?.pause()
        } catch (e: Exception) {
            Logcat.e("暂停播放失败", e)
        }
    }

    /**
     * 恢复播放
     */
    fun resume() {
        try {
            audioPlayer?.resume()
        } catch (e: Exception) {
            Logcat.e("恢复播放失败", e)
        }
    }

    /**
     * 停止播放并清空队列
     */
    fun stop() {
        try {
            audioPlayer?.stop()
        } catch (e: Exception) {
            Logcat.e("停止播放失败", e)
        }
    }

    /**
     * 跳过当前音频
     */
    fun skipCurrent() {
        try {
            audioPlayer?.skipCurrent()
        } catch (e: Exception) {
            Logcat.e("跳过当前音频失败", e)
        }
    }

    /**
     * 获取队列长度
     */
    fun getQueueSize(): Int {
        return try {
            audioPlayer?.getQueueSize() ?: 0
        } catch (e: Exception) {
            Logcat.e("获取队列长度失败", e)
            0
        }
    }

    /**
     * 获取当前播放状态
     */
    fun getCurrentState(): AudioQueuePlayer.AudioPlaybackState {
        return try {
            audioPlayer?.getCurrentState() ?: AudioQueuePlayer.AudioPlaybackState.IDLE
        } catch (e: Exception) {
            Logcat.e("获取播放状态失败", e)
            AudioQueuePlayer.AudioPlaybackState.IDLE
        }
    }

    /**
     * 设置播放状态监听器
     */
    fun setOnPlaybackStateChangedListener(listener: (AudioQueuePlayer.AudioPlaybackState) -> Unit) {
        try {
            audioPlayer?.setOnPlaybackStateChangedListener(listener)
        } catch (e: Exception) {
            Logcat.e("设置播放状态监听器失败", e)
        }
    }

    /**
     * 设置播放错误监听器
     */
    fun setOnPlaybackErrorListener(listener: (String, Exception?) -> Unit) {
        try {
            audioPlayer?.setOnPlaybackErrorListener(listener)
        } catch (e: Exception) {
            Logcat.e("设置播放错误监听器失败", e)
        }
    }

    /**
     * 设置队列变化监听器
     */
    fun setOnQueueChangedListener(listener: (Int) -> Unit) {
        try {
            audioPlayer?.setOnQueueChangedListener(listener)
        } catch (e: Exception) {
            Logcat.e("设置队列变化监听器失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            audioPlayer?.release()
            audioPlayer = null
            isInitialized = false
            Logcat.d("音频播放器资源已释放")
        } catch (e: Exception) {
            Logcat.e("释放音频播放器资源失败", e)
        }
    }
}
