package com.haoxue.libcommon.utils

import android.content.Context
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions

object PermissionHelper {
    fun requestPermission(context: Context, permissions: ArrayList<String>,callback:(<PERSON><PERSON>an)->Unit) {
        XXPermissions.with(context)
            // 申请单个权限
            .permission(permissions)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                    callback(allGranted)
                    if (allGranted) {
                        //相机权限已获取
                    } else {
                        //获取部分权限成功，但部分权限未正常授予
                    }
                }

                override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {

                }
            })
    }
}