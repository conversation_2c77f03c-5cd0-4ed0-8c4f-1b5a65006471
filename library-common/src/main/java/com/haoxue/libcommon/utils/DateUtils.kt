package com.haoxue.libcommon.utils

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.util.*

/**
 * 时间格式化工具类
 *
 * <AUTHOR>
 * @date on 2019/05/08
 */
@SuppressLint("SimpleDateFormat")
object DateUtils {


    @JvmStatic
    fun getTimeForLong(time: Long): String {
        return SimpleDateFormat("yyyy-MM-dd").format(Date(time))
    }


    @JvmStatic
    fun getTimeForLongYearAndMOnth(time: Long): String {
        return SimpleDateFormat("yyyy-MM").format(Date(time))
    }


    @JvmStatic
    fun getTimeForDate(time: Date): String {
        return SimpleDateFormat("yyyy-MM-dd").format(time)
    }


    @JvmStatic
    fun getTimeForLongHourAndMinute(time: Long): String {
        return SimpleDateFormat("HH:mm").format(Date(time))
    }

    @JvmStatic
    fun getTimeForLongMonthAndDay(time: Long): String {
        return SimpleDateFormat("MM月dd日").format(Date(time))
    }

    @JvmStatic
    fun getTimeForLongMonthAndDayAndHour(time: Long): String {
        return SimpleDateFormat("MM月dd日 HH:mm").format(Date(time))
    }


    @JvmStatic
    fun getLongForString(string: String): Long {
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm")
        val date = format.parse(string)
        return date.time
    }

    @JvmStatic
    fun getLongForStringTwo(string: String): Long {
        val format = SimpleDateFormat("yyyy-MM-dd")
        val date = format.parse(string)
        return date.time
    }

    @JvmStatic
    fun getDateForString(string: String): Date? {
        try {
            val format = SimpleDateFormat("yyyy-MM-dd")
            return format.parse(string)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return null
    }


    @JvmStatic
    fun getTimeArrayForlong(time: Long): List<String> {
        return SimpleDateFormat("yyyy-MM-dd-HH-mm").format(Date(time)).split("-")

    }

    /**
     * @param time 时间
     * @return time long类型日期格式化-带时分秒
     */
    @JvmStatic
    fun getStringTimeForLong(time: Long): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date(time))
    }


    /**
     * @param
     * @return time long类型日期格式化、时、分、秒
     */
    @JvmStatic
    fun getStringTimeForHMS(time: Long): String {
        val second = time % 60
        val min = time / 60 % 60
        val hour = time / 60 / 60

        val s = if (second >= 10) second.toString() else "0$second"
        val m = if (min >= 10) min.toString() else "0$min"
        val h = if (hour >= 10) hour.toString() else "0$hour"

        return "$h:$m:$s"
    }

    /**
     * @param
     * @return time long类型日期格式化、时、分、秒
     */
    @JvmStatic
    fun getStringTimeForHMSRemoveInsufficient(time: Long): String {
        val second = time % 60
        val min = time / 60 % 60
        val hour = time / 60 / 60

        val s = if (second >= 10) second.toString() else "0$second"
        val m = if (min >= 10) min.toString() else "0$min"
        val h = if (hour >= 10) hour.toString() else "0$hour"

        if (h == "00")
            return "$m:$s"

        return "$h:$m:$s"
    }

    /**
     * @return time long类型日期格式化-只有天数
     */
    fun getStringTimeForDateJustYear(time: Date): String {
        return SimpleDateFormat("yyyy").format(time)
    }

    /**
     * @return time long类型日期格式化-只有天数
     */
    fun getStringTimeForDateJustMonth(time: Date): String {
        return SimpleDateFormat("MM").format(time)
    }


    /**
     * @return time long类型日期格式化-只有天数
     */
    @JvmStatic
    fun getStringTimeForDateJustDay(time: Long): String {
        return SimpleDateFormat("dd").format(Date(time))
    }

    /**
     * @param
     * @return time Data类型日期格式化-带时分
     */
    fun getStringTimeForLongNoSecond(time: Long): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm").format(time)
    }

    @JvmStatic
    fun getDateIsToday(time: Long): Boolean {
        val nowTimeText = getTimeForLong(System.currentTimeMillis())
        return getTimeForLong(time) == nowTimeText
    }

    @JvmStatic
    fun getDateString(time: Long): String {
        val nowTimeText = getTimeForLong(System.currentTimeMillis())
        val lastTimeText = getTimeForLong(System.currentTimeMillis() - 24 * 60 * 60 * 1000)

        return when (getTimeForLong(time)) {
            nowTimeText -> {
                getTimeForLongHourAndMinute(time)
            }
            lastTimeText -> {
                "昨天"
            }
            else -> {
                getTimeForLongMonthAndDay(time)
            }
        }
    }

}
