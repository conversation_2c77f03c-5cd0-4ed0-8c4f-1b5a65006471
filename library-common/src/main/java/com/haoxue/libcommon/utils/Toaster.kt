package com.haoxue.libcommon.utils

import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.StringRes
import com.haoxue.libcommon.BaseCommonApp
import com.haoxue.libcommon.R
import com.haoxue.libcommon.dp2px
import java.lang.Exception

/**
 * 单例轻提示
 */
object Toaster {

    @JvmStatic
    fun toast(@StringRes toastRes: Int) {
        nativeToast(BaseCommonApp.mContext.resources.getString(toastRes))
    }


    @JvmStatic
    fun toast(toast: String?) {
        nativeToast(toast)
    }

    @JvmStatic
    fun shortToaster(toast: String?) {
        nativeToast(toast)
    }

    private fun nativeToast(content: String?) {
        try {
            if (TextUtils.isEmpty(content))
                return
            val toast = Toast(BaseCommonApp.mContext)
            val layout = LayoutInflater.from(BaseCommonApp.mContext)
                .inflate(R.layout.common_toast, null, false)
            layout.findViewById<TextView>(R.id.text).text = content
            toast.view = layout
            toast.setGravity(Gravity.CENTER, 0, 160.dp2px())
            toast.duration = Toast.LENGTH_SHORT
            toast.show()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
