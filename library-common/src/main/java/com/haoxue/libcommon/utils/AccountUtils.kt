package com.haoxue.libcommon.utils

import com.haoxue.libcommon.bean.UserInfoBean
import com.orhanobut.hawk.Hawk


/**
 * 用户账户信息相关操作类
 */
object AccountUtils {

    private const val USER_INFO_EVENT_HAWK = "USER_INFO_EVENT_HAWK"

    /**
     * 退出登录时清除本地保存的用户信息
     */
    @JvmStatic
    fun logout() {
        Hawk.put(USER_INFO_EVENT_HAWK, null)
    }

    /**
     * 获得用户账户信息
     */
    @JvmStatic
    fun getUserInfo(): UserInfoBean = Hawk.get<UserInfoBean>(USER_INFO_EVENT_HAWK) ?: UserInfoBean()


    @JvmOverloads
    @JvmStatic
    fun isLogin(goLogin: Boolean = false): Boolean {

        return false
    }

    /**
     * 保存用户账户信息
     */
    @JvmStatic
    fun saveUserInfo(userInfoBean: UserInfoBean?) {
        Hawk.put(USER_INFO_EVENT_HAWK, userInfoBean)
    }


}