package com.haoxue.libcommon.utils

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.graphics.Point
import android.os.Build
import android.util.DisplayMetrics
import android.view.WindowManager
import com.haoxue.libcommon.BaseCommonApp

object PixelUtils {

    /**
     * 判断屏幕方向是否为竖向
     *
     * @return
     */
    @JvmStatic
    val isPrortrait: <PERSON>ole<PERSON>
        get() {
            val mConfiguration = BaseCommonApp.mContext.resources.configuration
            return mConfiguration.orientation == Configuration.ORIENTATION_PORTRAIT
        }

    /**
     * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
     */
    @JvmStatic
    fun px2dip(pxValue: Float): Int {
        val scale = BaseCommonApp.mContext.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    /**
     * 获取屏幕宽度，单位为px
     * @return 返回int类型的屏幕宽度
     */
    @JvmStatic
    fun getScreenWidth(): Int {
        val manager = BaseCommonApp.mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = manager.defaultDisplay
        val outMetrics = DisplayMetrics()
        defaultDisplay.getMetrics(outMetrics)
        return outMetrics.widthPixels
    }

    /**
     * 获取屏幕高度，单位为px
     * @return 返回int类型的屏幕高度
     */
    @JvmStatic
    fun getScreenHeight(): Int {
        val manager = BaseCommonApp.mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = manager.defaultDisplay
        val outMetrics = DisplayMetrics()
        defaultDisplay.getMetrics(outMetrics)
        return outMetrics.heightPixels
    }

    /**
     * 获取屏幕高度，单位为px
     * @return 返回int类型的屏幕高度
     */
    @JvmStatic
    fun getScreenRealHeight(): Int {
        val manager = BaseCommonApp.mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val defaultDisplay = manager.defaultDisplay
        val outMetrics = Point()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            defaultDisplay.getRealSize(outMetrics)
        }else{
            defaultDisplay.getSize(outMetrics)
        }
        return outMetrics.y
    }

    /**
     * 获得状态栏高度，默认为60px
     */
    @JvmStatic
    fun getStateBarHeight(): Int {
        var result = 0
        val resourceId = BaseCommonApp.mContext.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = BaseCommonApp.mContext.resources.getDimensionPixelSize(resourceId)
        }
        return if (0 == result) dip2px(20f) else result
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    @JvmStatic
    fun dip2px(dpValue: Float): Int {
        val scale = BaseCommonApp.mContext.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    /**
     * 获取虚拟导航键的高度
     *
     * @param context
     * @return
     */
    @JvmStatic
    fun getNavigationBarHeight(): Int {
        var result = 0
        try {
            val resourceId = BaseCommonApp.mContext.resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) result = BaseCommonApp.mContext.resources.getDimensionPixelSize(resourceId)
        } catch (e: Exception) {
        }
        return result
    }

    /**
     * 去除状态栏
     *
     * @param context
     */
    @JvmStatic
    fun removeTitleView(context: Context) {
        (context as Activity).window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
    }

}
