package com.haoxue.libcommon.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.text.TextUtils

object DeviceUtils {
    private var versionName: String? = null

    val systemVersion: String?
        /**
         * 获取当前手机系统版本号
         *
         * @return 系统版本号
         */
        get() = Build.VERSION.RELEASE

    val deviceManufacturer: String?
        /**
         * 获取设备制造商
         */
        get() = Build.MANUFACTURER

    val systemModel: String?
        /**
         * 获取手机型号
         */
        get() = Build.MODEL

    val deviceBrand: String?
        /**
         * 获取手机厂商
         *
         * @return 手机厂商
         */
        get() = Build.BRAND


    /**
     * 获取当前本地apk的版本
     */
    fun getVersionCode(mContext: Context): Int {
        var versionCode = 0
        try {
            //获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionCode = mContext.packageManager.getPackageInfo(mContext.packageName, 0).versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return versionCode
    }

    /**
     * 获取当前本地apk的版本
     */
    fun getVersionName(mContext: Context): String? {
        if (!TextUtils.isEmpty(versionName)) {
            return versionName
        }
        try {
            //获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionName = mContext.getPackageManager()
                .getPackageInfo(mContext.getPackageName(), 0).versionName
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return versionName
    }

    /**
     * 获取 AndroidID
     */
    fun getAndroidID(context: Context): String? {
        return Settings.System.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID)
    }
}

