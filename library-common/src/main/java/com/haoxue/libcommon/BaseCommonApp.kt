package com.haoxue.libcommon

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import com.haoxue.libcommon.utils.ThirdSdk
import kotlin.properties.Delegates

/**
 * 单例模式的application基类
 */
open class BaseCommonApp : Application(), Application.ActivityLifecycleCallbacks {

    companion object {
        @JvmStatic
        var mContext: Context by Delegates.notNull()

        @JvmStatic
        var isBackGround = false

        private var startCount = 0
    }

    override fun onCreate() {
        super.onCreate()
        mContext = applicationContext
        ThirdSdk.initData(this)
        registerActivityLifecycleCallbacks(this)
    }


    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityStarted(activity: Activity) {
        startCount++
        if (startCount == 1) {
            isBackGround = false

        }
    }

    override fun onActivityResumed(activity: Activity) {
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
        startCount--
        if (startCount == 0) {
            isBackGround = true
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
    }

}
