package com.haoxue.libcommon


import android.annotation.SuppressLint
import android.app.Activity
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.haoxue.libcommon.utils.PixelUtils
import com.haoxue.libcommon.utils.Toaster

fun Any.getDrawableX(@DrawableRes res: Int) = ContextCompat.getDrawable(BaseCommonApp.mContext, res)

fun Any.getColorX(@ColorRes res: Int) = ContextCompat.getColor(BaseCommonApp.mContext, res)

/** toast相关 **/
fun Any.shortToast(msg: String?) {
    Toaster.shortToaster(msg)
}

fun Any.longToast(msg: String?) {
    Toaster.toast(msg)
}

fun <T : View> T.singleClick(block: (T) -> Unit) {
    setOnClickListener {
        if (clickEnable()) {
            block(it as T)
        }
    }
}


var triggerLastTime = 0L
private fun <T : View> T.clickEnable(): Boolean {
    var flag = false
    val currentClickTime = System.currentTimeMillis()
    if (currentClickTime - triggerLastTime >= 400L) {
        flag = true
    }
    triggerLastTime = currentClickTime
    return flag
}


fun TabLayout.setSelectListener(call: (position: Int) -> Unit) {
    this.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
        override fun onTabReselected(p0: TabLayout.Tab?) {
        }

        override fun onTabUnselected(p0: TabLayout.Tab?) {
        }

        override fun onTabSelected(p0: TabLayout.Tab?) {
            call(selectedTabPosition)
        }
    })
}

fun EditText.onTextChangeListener(call: (text: CharSequence?) -> Unit) {
    this.addTextChangedListener(object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            call(s)
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }
    })
}

@JvmOverloads
fun EditText.onActionListener(call: () -> Unit, action: Int = EditorInfo.IME_ACTION_GO) {
    this.setOnEditorActionListener(object : TextView.OnEditorActionListener {
        override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            var action1 = event?.action
            if (actionId == action) {
                call()
                return true
            }
            return false
        }
    })
}


fun ViewPager.setSelectListener(call: (position: Int) -> Unit) {
    this.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        }

        override fun onPageSelected(position: Int) {
            call(position)
        }

        override fun onPageScrollStateChanged(state: Int) {
        }
    })
}

@SuppressLint("ClickableViewAccessibility")
fun EditText.scrollInScrollView() {
    this.setOnTouchListener { view, event ->
        if ((view.id == <EMAIL> && canVerticalScroll(this@scrollInScrollView))) {
            view.parent.requestDisallowInterceptTouchEvent(true)
            if (event.action == MotionEvent.ACTION_UP) {
                view.parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        false
    }
}

fun View.gone() {
    this.visibility = View.GONE
}

fun View.visible() {
    this.visibility = View.VISIBLE
}

fun View.inVisible() {
    this.visibility = View.INVISIBLE
}

fun View.isVisible() = this.visibility == View.VISIBLE
fun View.isGone() = this.visibility == View.GONE
fun View.isInVisible() = this.visibility == View.INVISIBLE


private fun canVerticalScroll(editText: EditText): Boolean {
    val scrollY = editText.scrollY
    val scrollRange = editText.layout.height
    val scrollExtent = editText.height - editText.compoundPaddingTop - editText.compoundPaddingBottom
    val scrollDifference = scrollRange - scrollExtent
    return if (scrollDifference == 0) {
        false
    } else scrollY > 0 || scrollY < scrollDifference - 1
}

fun Number.dp2px() = PixelUtils.dip2px(this.toFloat())
fun Number.px2dp() = PixelUtils.px2dip(this.toFloat())

fun Int.toZeroString() = if (this < 10) "0${this}" else this.toString()

fun Activity.inflaterView(id: Int): View = this.layoutInflater.inflate(id, null, false)
