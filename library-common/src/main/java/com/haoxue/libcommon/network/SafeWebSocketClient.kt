package com.haoxue.libcommon.network

import com.lazy.library.logging.Logcat
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.plugins.websocket.*
import io.ktor.websocket.*
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 安全的 WebSocket 客户端
 * 具有完善的异常处理，防止 app 闪退
 */
object SafeWebSocketClient {

    private var webSocketSession: DefaultClientWebSocketSession? = null
    private var connectionJob: Job? = null
    private var isConnected = false

    /**
     * 安全连接 WebSocket 服务器
     * 包含完善的异常处理，防止 app 闪退
     */
    suspend fun connectSafely(
        url: String,
        headerList: Map<String, String> = emptyMap(),
        onMessage: (String) -> Unit,
        onConnected: () -> Unit = {},
        onDisconnected: () -> Unit = {},
        onError: (String, Throwable?) -> Unit = { _, _ -> }
    ) {
        try {
            Logcat.d("WebSocket 安全连接开始: $url")

            // 验证 URL 格式
            if (!isValidWebSocketUrl(url)) {
                val error = "无效的 WebSocket URL: $url"
                Logcat.e(error)
                onError(error, IllegalArgumentException(error))
                return
            }

            // 先安全断开之前的连接
            disconnectSafely()

            connectionJob = CoroutineScope(Dispatchers.IO + SupervisorJob()).launch {
                try {
                    KtorClient.client.webSocket(
                        urlString = url,
                        request = {
                            // 安全添加请求头
                            headerList.forEach { (key, value) ->
                                try {
                                    headers.append(key, value)
                                } catch (e: Exception) {
                                    Logcat.e("添加请求头失败: $key = $value", e)
                                }
                            }
                        }
                    ) {
                        webSocketSession = this
                        isConnected = true

                        // 安全调用连接成功回调
                        withContext(Dispatchers.Main) {
                            try {
                                onConnected()
                            } catch (e: Exception) {
                                Logcat.e("连接成功回调异常", e)
                            }
                        }

                        Logcat.d("WebSocket 连接成功")

                        try {
                            // 监听消息
                            for (frame in incoming) {
                                try {
                                    when (frame) {
                                        is Frame.Text -> {
                                            val text = frame.readText()
//                                            Logcat.d("WebSocket 收到消息: ${text.take(100)}...")
                                            withContext(Dispatchers.Main) {
                                                try {
                                                    onMessage(text)
                                                } catch (e: Exception) {
                                                    Logcat.e("消息处理回调异常", e)
                                                }
                                            }
                                        }

                                        is Frame.Binary -> {
                                            val data = frame.readBytes()
                                            Logcat.d("WebSocket 收到二进制数据: ${data.size} bytes")

                                            withContext(Dispatchers.Main) {
                                                try {
                                                    onMessage("Binary data: ${data.size} bytes")
                                                } catch (e: Exception) {
                                                    Logcat.e("二进制消息处理回调异常", e)
                                                }
                                            }
                                        }

                                        is Frame.Close -> {
                                            val reason = frame.readReason()
                                            Logcat.d("WebSocket 连接关闭: ${reason?.message}")

                                            withContext(Dispatchers.Main) {
                                                try {
                                                    onDisconnected()
                                                } catch (e: Exception) {
                                                    Logcat.e("断开连接回调异常", e)
                                                }
                                            }
                                            break
                                        }

                                        else -> {
                                            Logcat.d("WebSocket 收到其他类型消息: ${frame.frameType}")
                                        }
                                    }
                                } catch (e: Exception) {
                                    Logcat.e("处理 WebSocket 帧异常", e)
                                    // 继续处理下一个帧，不中断连接
                                }
                            }
                        } catch (e: ClosedReceiveChannelException) {
                            Logcat.d("WebSocket 连接正常关闭")
                            withContext(Dispatchers.Main) {
                                try {
                                    onDisconnected()
                                } catch (ex: Exception) {
                                    Logcat.e("断开连接回调异常", ex)
                                }
                            }
                        } catch (e: Exception) {
                            Logcat.e("WebSocket 消息循环异常", e)
                            withContext(Dispatchers.Main) {
                                try {
                                    onError("消息接收异常: ${e.message}", e)
                                } catch (ex: Exception) {
                                    Logcat.e("错误回调异常", ex)
                                }
                            }
                        } finally {
                            isConnected = false
                            webSocketSession = null
                        }
                    }
                } catch (e: Exception) {
                    isConnected = false
                    webSocketSession = null

                    val errorMessage = when (e) {
                        is ConnectException -> "连接被拒绝，请检查服务器地址和端口"
                        is SocketTimeoutException -> "连接超时，请检查网络连接"
                        is UnknownHostException -> "无法解析主机名，请检查服务器地址"
                        else -> "连接失败: ${e.message}"
                    }

                    Logcat.e("WebSocket 连接异常: $errorMessage", e)

                    withContext(Dispatchers.Main) {
                        try {
                            onError(errorMessage, e)
                        } catch (ex: Exception) {
                            Logcat.e("错误回调异常", ex)
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Logcat.e("WebSocket 连接启动失败", e)
            isConnected = false

            try {
                onError("连接启动失败: ${e.message}", e)
            } catch (ex: Exception) {
                Logcat.e("错误回调异常", ex)
            }
        }
    }

    /**
     * 安全发送文本消息
     */
    suspend fun sendTextSafely(text: String): Result<Unit> {
        return try {
            if (!isConnected || webSocketSession == null) {
                Result.failure(IllegalStateException("WebSocket 未连接"))
            } else {
                webSocketSession?.send(Frame.Text(text))
                Logcat.d("WebSocket 发送消息成功: ${text.take(50)}...")
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Logcat.e("WebSocket 发送消息失败", e)
            Result.failure(e)
        }
    }

    /**
     * 安全断开连接
     */
    suspend fun disconnectSafely() {
        try {
            webSocketSession?.close(CloseReason(CloseReason.Codes.NORMAL, "客户端主动断开"))
            connectionJob?.cancel()
            isConnected = false
            webSocketSession = null
            Logcat.d("WebSocket 安全断开连接")
        } catch (e: Exception) {
            Logcat.e("WebSocket 断开连接异常", e)
            // 即使断开失败也要重置状态
            isConnected = false
            webSocketSession = null
        }
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean = isConnected && webSocketSession != null

    /**
     * 验证 WebSocket URL 格式
     */
    private fun isValidWebSocketUrl(url: String): Boolean {
        return try {
            url.startsWith("ws://") || url.startsWith("wss://")
        } catch (e: Exception) {
            false
        }
    }
}
