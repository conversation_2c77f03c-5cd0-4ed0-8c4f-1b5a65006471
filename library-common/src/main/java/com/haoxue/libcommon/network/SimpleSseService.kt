package com.haoxue.libcommon.network

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.utils.io.*
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.channels.awaitClose
import kotlinx.serialization.json.Json

/**
 * 简化的 SSE 服务类
 * 使用原始 HTTP 流处理 SSE 数据
 */
class SimpleSseService {
    
    private val client = KtorClient.client
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    /**
     * 调用 SSE 接口
     */
    suspend fun callSseStream(
        question: String,
        callback: AiStreamCallback
    ) {
        try {
            callback.onStart()
            val response = client.get("http://192.168.5.60:8189/ai/askString") {
                // 设置请求头
                header("token", "7dc48c0a2d794e44ba36750ffea65cda")
                header("unitId", "1")
                header("pkId", "2")
                header("role", "3")
                header("Accept", "text/event-stream")
                header("Cache-Control", "no-cache")
                header("Connection", "keep-alive")
                header("Content-Type", "application/json")

                parameter("message", question)
                parameter("questionId", System.currentTimeMillis())
                parameter("conversationId", System.currentTimeMillis())
            }
            
            val fullResponse = StringBuilder()
            val channel = response.bodyAsChannel()
            
            while (!channel.isClosedForRead) {
                val line = channel.readUTF8Line()
                if (line != null) {
                    when {
                        line.startsWith("data:") -> {
                            val data = line.substring(5) // 移除 "data: " 前缀
                            
                            if (data == "[DONE]") {
                                callback.onComplete(fullResponse.toString())
                                break
                            }
                            
                            if (data.isNotBlank()) {

                                Logcat.d(data)
                                try {
                                    // 尝试解析 JSON 响应
                                    val aiResponse = json.decodeFromString<AiResponseData>(data)
                                    val content = aiResponse.choices.firstOrNull()?.delta?.content

                                    if (!content.isNullOrEmpty()) {
                                        fullResponse.append(content)
                                        callback.onChunk(content)
                                    }

                                    // 检查是否完成
                                    val finishReason = aiResponse.choices.firstOrNull()?.finishReason
                                    if (finishReason != null) {
                                        callback.onComplete(fullResponse.toString())
                                        break
                                    }

                                } catch (e: Exception) {
                                    // 如果不是标准格式，直接作为文本处理
                                    fullResponse.append(data)
                                    callback.onChunk(data)
                                }
                            }
                        }
                        
                        line.startsWith("event: ") -> {
                            val event = line.substring(7) // 移除 "event: " 前缀
                            Logcat.d("SSE 事件: $event")
                            
                            if (event == "error") {
                                callback.onError(Exception("服务器返回错误事件"))
                            }
                        }
                        
                        line.isEmpty() -> {
                            // 空行，事件分隔符
                            continue
                        }
                        
                        else -> {
                            // 其他格式的数据
                            if (line.isNotBlank()) {
                                fullResponse.append(line)
                                callback.onChunk(line)
                            }
                        }
                    }
                }
            }
            
            // 如果循环正常结束，调用完成回调
            if (fullResponse.isNotEmpty()) {
                callback.onComplete(fullResponse.toString())
            }
            
        } catch (e: Exception) {
            Logcat.e("SSE 连接失败", e)
            callback.onError(e)
        }
    }
    
    /**
     * 简化调用方法
     */
    suspend fun askSimple(
        question: String,
        onMessage: (String) -> Unit,
        onComplete: (String) -> Unit = {},
        onError: (Throwable) -> Unit = {}
    ) {
        callSseStream(
            question = question,
            callback = object : AiStreamCallback {
                override fun onStart() {
                    Logcat.d("发起SSE请求")
                }
                
                override fun onChunk(content: String) {
                    onMessage(content)
                }
                
                override fun onComplete(fullResponse: String) {
                    onComplete(fullResponse)
                }
                
                override fun onError(error: Throwable) {
                    onError(error)
                }
            }
        )
    }
    
    /**
     * Flow 方式调用
     */
    fun askFlow(
        question: String,
    ): Flow<String> = callbackFlow {
        callSseStream(
            question = question,
            callback = object : AiStreamCallback {
                override fun onStart() {
                    // Flow 开始
                }
                
                override fun onChunk(content: String) {
                    trySend(content)
                }
                
                override fun onComplete(fullResponse: String) {
                    close()
                }
                
                override fun onError(error: Throwable) {
                    close(error)
                }
            }
        )
        
        awaitClose {
            // 清理资源
        }
    }
    
    companion object {
        @Volatile
        private var INSTANCE: SimpleSseService? = null
        
        fun getInstance(): SimpleSseService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SimpleSseService().also { INSTANCE = it }
            }
        }
    }
}
