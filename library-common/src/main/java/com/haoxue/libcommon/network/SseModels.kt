package com.haoxue.libcommon.network

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * AI 响应数据模型
 */
@Serializable
data class AiResponseData(
    val id: String,
    val created: Long,
    val model: String,
    val choices: List<Choice>
)

/**
 * 选择项模型
 */
@Serializable
data class Choice(
    val index: Int,
    val delta: Delta,
    @SerialName("finish_reason")
    val finishReason: String? = null
)

/**
 * 增量数据模型
 */
@Serializable
data class Delta(
    val role: String? = null,
    val content: String? = null
)

/**
 * AI 流式响应回调
 */
interface AiStreamCallback {
    fun onStart()
    fun onChunk(content: String)
    fun onComplete(fullResponse: String)
    fun onError(error: Throwable)
}
