package com.haoxue.libcommon.network

import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.plugins.websocket.*
import io.ktor.websocket.*
import com.haoxue.libcommon.BuildConfig
import com.lazy.library.logging.Logcat
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.channels.awaitClose
import kotlinx.serialization.json.Json

/**
 * 简化的 WebSocket 客户端
 * 使用 OkHttp 引擎，支持 WebSocket 功能
 */
object KtorClient {
    
    val client = HttpClient(OkHttp) {
        install(WebSockets) {
            pingIntervalMillis = 20_000L
//            maxFrameSize = Long.MAX_VALUE
        }

        // JSON 序列化配置
        install(ContentNegotiation) {
            json(Json {
                prettyPrint = BuildConfig.IS_DEBUG_MODE  // 调试模式下美化 JSON
                isLenient = true                         // 宽松模式，容错性更好
                ignoreUnknownKeys = true                 // 忽略未知字段，避免解析失败
                allowSpecialFloatingPointValues = true  // 允许 NaN、Infinity 等特殊值
                encodeDefaults = false                   // 不编码默认值，减少数据量
            })
        }
        
        install(Logging) {
            logger = Logger.DEFAULT
            level = if (BuildConfig.IS_DEBUG_MODE) LogLevel.ALL else LogLevel.NONE
        }
        
        engine {
            config {
                connectTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                readTimeout(0, java.util.concurrent.TimeUnit.SECONDS)
                writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
            }
        }
    }
}
