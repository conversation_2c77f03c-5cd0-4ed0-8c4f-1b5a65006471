package com.haoxue.sportai

import androidx.lifecycle.lifecycleScope
import com.haoxue.libcommon.ConstData
import com.haoxue.libcommon.markdown.BasicMarkdownRenderer
import com.haoxue.libcommon.markdown.BasicMarkdownUtils
import com.haoxue.libcommon.markdown.BasicChatAdapter
import com.haoxue.libcommon.markdown.BasicMarkdownRecyclerUtils
import com.haoxue.libcommon.singleClick
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.libcommon.utils.AudioPlaybackUtils
import com.haoxue.libcommon.utils.SafeWebSocketUtils
import com.haoxue.libcommon.utils.SseUtils
import com.haoxue.mcpserver.McpServerHelper
import com.haoxue.mcpserver.ToolsUtils
import com.haoxue.sportai.databinding.ActivityMcpBinding
import com.haoxue.stt.SttHelper
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job

class McpActivity : BaseActivity<ActivityMcpBinding>(R.layout.activity_mcp) {
    // 使用基础的聊天适配器
    private lateinit var chatAdapter: BasicChatAdapter

    // Markdown 相关
    private val markdownBuilder = StringBuilder()
    private var isStreamingMarkdown = false

    // STT 防抖和文本拼接相关
    private var sttDebounceJob: Job? = null
    private val sttTextBuilder = StringBuilder()
    private var lastSttText = ""
    private val sttDebounceDelayMs = 1200L // 防抖延迟

    override fun initCommonData() {
        mBinding.add.singleClick {
            SttHelper.startRecord(this, { isRecording ->
                mBinding.add.text = if (isRecording) "结束" else "开始"

                // 如果停止录音，立即处理拼接的完整文本
                if (!isRecording) {
                    handleSttTextWithDebounce("", forceExecute = true)
                }
            }) { recognizedText ->
                Logcat.d("stt---${recognizedText}")

                // 使用防抖机制处理STT文本
                handleSttTextWithDebounce(recognizedText)
            }
        }
        // 滚动到底部按钮
        mBinding.scrollToBottomFab.singleClick {
            BasicMarkdownRecyclerUtils.forceScrollToBottom(mBinding.recyclerView)
        }

        // 长按滚动按钮触发WebSocket重连
        mBinding.scrollToBottomFab.setOnLongClickListener {
            SafeWebSocketUtils.manualReconnect()
            Logcat.d("手动触发WebSocket重连")
            true
        }
    }

    /**
     * STT 文本防抖处理 - 支持文本拼接
     * @param text 识别到的文本
     * @param forceExecute 是否强制执行（用于录音结束时）
     */
    private fun handleSttTextWithDebounce(text: String, forceExecute: Boolean = false) {
        // 如果识别到新的文本片段，立即打断当前 TTS 播放并清空队列，准备后续新语音
        if (text.isNotBlank()) {
            runOnUiThread {
                AudioPlaybackUtils.stop()
            }
        }

        // 取消之前的防抖任务
        sttDebounceJob?.cancel()

        if (forceExecute) {
            // 强制执行（录音结束时），处理已拼接的完整文本
            val completeText = sttTextBuilder.toString().trim()
            if (completeText.isNotBlank()) {
                processSttText(completeText)
            }
            // 清空文本构建器
            sttTextBuilder.clear()
            lastSttText = ""
            return
        }

        if (text.isBlank()) return

        // 检查是否是新的文本片段
        if (text != lastSttText) {
            // 智能拼接文本
            appendSttText(text)
            lastSttText = text
        }

        // 启动新的防抖任务
        sttDebounceJob = lifecycleScope.launch {
            try {
                delay(sttDebounceDelayMs)

                // 延迟后处理拼接的完整文本
                val completeText = sttTextBuilder.toString().trim()
                if (completeText.isNotBlank()) {
                    processSttText(completeText)

                    // 清空文本构建器，准备下一轮
                    sttTextBuilder.clear()
                    lastSttText = ""
                }
            } catch (e: Exception) {
                Logcat.e("STT 防抖处理异常", e)
            }
        }

//        Logcat.d("STT 防抖: 收到文本 '$text'，当前拼接文本: '${sttTextBuilder.toString().trim()}'，等待 ${sttDebounceDelayMs}ms 后处理")
    }

    /**
     * 智能拼接 STT 文本
     */
    private fun appendSttText(newText: String) {
        val currentText = sttTextBuilder.toString().trim()

        when {
            // 如果是空的，直接添加
            currentText.isEmpty() -> {
                sttTextBuilder.append(newText)
                Logcat.d("STT 拼接: 初始文本: '$newText'")
            }
            else -> {
                // 检查是否需要添加标点符号或空格
                val needsSpace = !currentText.endsWith(" ") &&
                                !currentText.endsWith("，") &&
                                !currentText.endsWith("。") &&
                                !currentText.endsWith("？") &&
                                !currentText.endsWith("！")
                if (needsSpace) {
                    sttTextBuilder.append("，")
                }
                sttTextBuilder.append(newText)
                Logcat.d("STT 拼接: 添加新文本片段: '$newText'")
            }
        }
    }

    /**
     * 处理 STT 识别的文本
     */
    private fun processSttText(text: String) {
        if (text.isBlank()) return

        Logcat.d("STT 处理: 开始处理完整文本 '$text'")

        runOnUiThread {
            // 添加用户消息
            val userMessage = BasicChatAdapter.ChatMessage(
                type = 0, // 用户消息
                content = text,
                isMarkdown = false
            )
            chatAdapter.addMessage(userMessage)

            // 自动滚动到底部
            BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)

            val result = ToolsUtils.interceptFun(text)
            if (result != null){
                val userMessage = BasicChatAdapter.ChatMessage(
                    type = 1, // 用户消息
                    content = result,
                    isMarkdown = false
                )
                chatAdapter.addMessage(userMessage)
                return@runOnUiThread
            }


            // 调用 AI 回复
            callSseExample(question = text)
        }
    }



    /**
     * SSE 调用示例 - 使用优化的 Markwon RecyclerView 适配器
     */
    private fun callSseExample(question: String = "") {
        // 重置 Markdown 构建器
        markdownBuilder.clear()
        isStreamingMarkdown = true

        // 添加 AI 回复的占位符
        val aiMessage = BasicChatAdapter.ChatMessage(
            type = 1, // AI 消息
            content = "",
            isMarkdown = false
        )
        chatAdapter.addMessage(aiMessage)

        SseUtils.quickAsk(
            lifecycleScope,
            question,
            onComplete = { completeResponse ->
                runOnUiThread {
                    Logcat.d("SSE 完整回答: $completeResponse")
                    isStreamingMarkdown = false

                    // 最终更新完整的 Markdown 内容
                    val isMarkdown = BasicMarkdownUtils.isMarkdown(completeResponse)
                    chatAdapter.updateLastMessage(completeResponse, isMarkdown)

                    // 自动滚动到底部
                    BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
                }
            },
            onError = { error ->
                runOnUiThread {
                    isStreamingMarkdown = false
                    chatAdapter.updateLastMessage("❌ 请求失败: $error", false)
                    Logcat.e("SSE 请求失败: $error")
                }
            },
            onMessage = { content ->
                runOnUiThread {
                    // 流式更新 Markdown 内容
                    markdownBuilder.append(content)

                    val currentContent = markdownBuilder.toString()
                    val isMarkdown = BasicMarkdownUtils.isMarkdown(currentContent)

                    // 更新最后一条消息
                    chatAdapter.updateLastMessage(currentContent, isMarkdown)

                    // 自动滚动到底部
                    BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
                }
            }
        )
    }



    override fun initCommonListener() {
        // 配置热词以提高识别准确率
        setupHotwords()

        SttHelper.init(this)
        McpServerHelper.init(this)
        AudioPlaybackUtils.initialize(this)

        // 初始化基础版 Markdown 渲染器
        BasicMarkdownRenderer.initialize(this)

        // 配置WebSocket自动重连
        SafeWebSocketUtils.configureReconnect(
            enabled = true,
            maxAttempts = -1,      // 无限重连，直到连接成功
            intervalMs = 2000L,    // 基础间隔2秒
            maxIntervalMs = 10000L // 最大间隔30秒
        )

        // 设置基础聊天适配器
        chatAdapter = BasicMarkdownRecyclerUtils.setupChatRecyclerView(
            mBinding.recyclerView,
            this,
            onItemClick = { message, position ->
                // 点击消息的处理
                Logcat.d("点击消息: ${message.content}")
            },
            onScrollStateChanged = { shouldShowScrollButton ->
                // 控制滚动到底部按钮的显示/隐藏
                runOnUiThread {
                    mBinding.scrollToBottomFab.visibility = if (shouldShowScrollButton) {
                        android.view.View.VISIBLE
                    } else {
                        android.view.View.GONE
                    }
                }
            }
        )
        SafeWebSocketUtils.safeConnect(
            lifecycleScope,
            ConstData.TTS_WEBSOCKET_URL,
            onMessage = { message ->
                Logcat.d("WebSocket 收到音频消息: $message")
                AudioPlaybackUtils.playAudio(message)
            },
            onConnected = {
                Logcat.d("🔗 WebSocket 音频连接成功")
                Logcat.d("📊 ${SafeWebSocketUtils.getReconnectInfo()}")
            },
            onDisconnected = {
                Logcat.d("❌ WebSocket 音频连接断开")
                // 连接断开时会自动尝试重连（如果启用）
            },
            onError = { error ->
                Logcat.e("🚨 WebSocket 音频连接错误: $error")
                // 错误时也会自动尝试重连（如果启用）
            }
        )

        // 设置音频播放队列变化监听器
        AudioPlaybackUtils.setOnQueueChangedListener { queueSize ->
            Logcat.d("音频队列长度变化: $queueSize")
        }

        // 设置音频播放状态监听器
        AudioPlaybackUtils.setOnPlaybackStateChangedListener { state ->
            Logcat.d("音频播放状态变化: $state")
        }

        // 设置音频播放错误监听器
        AudioPlaybackUtils.setOnPlaybackErrorListener { error, exception ->
            Logcat.e("音频播放错误: $error", exception)
        }
    }

    /**
     * 设置热词以提高语音识别准确率
     */
    private fun setupHotwords() {
        try {
            // 创建运动相关的热词列表
            val sportsHotwords = listOf(
                // 跳绳相关 - 最高优先级
                "开始跳绳" to 2.5f,
                "结束跳绳" to 2.5f,
                "停止跳绳" to 2.5f,
                "跳绳训练" to 2.0f,
                "跳绳运动" to 2.0f,

                // AI助手相关 - 高优先级
                "AI助手" to 2.0f,
                "智能助手" to 2.0f,
                "语音助手" to 2.0f,

                // 运动指令 - 中等优先级
                "运动建议" to 1.8f,
                "健身指导" to 1.8f,
                "健康状态" to 1.8f,
                "设备信息" to 1.5f,
                "当前时间" to 1.5f,

                // 常用动作 - 正常优先级
                "开始运动" to 1.3f,
                "结束运动" to 1.3f,
                "暂停运动" to 1.3f,
                "继续运动" to 1.3f
            )

            // 创建自定义热词文件
            SttHelper.createHotwordsFile(this, sportsHotwords)

            // 配置热词参数
            SttHelper.configureHotwords(
                hotwordsFile = "hotwords.txt",  // 使用assets中的基础热词文件
                hotwordsScore = 1.8f            // 全局热词权重
            )

            Logcat.d("热词配置完成，包含 ${sportsHotwords.size} 个运动相关热词")

        } catch (e: Exception) {
            Logcat.e("设置热词失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 取消 STT 防抖任务并清空文本构建器
        sttDebounceJob?.cancel()
        sttTextBuilder.clear()

        // 清理 RecyclerView 滚动监听器
        BasicMarkdownRecyclerUtils.cleanup(mBinding.recyclerView)

        SttHelper.release()
        McpServerHelper.stopServer()
        AudioPlaybackUtils.release()
    }

    override fun requestCommonData() {
    }
}