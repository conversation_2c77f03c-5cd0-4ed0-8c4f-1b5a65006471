# 🏃‍♂️ AI Sports - 智能运动助手

[![Android](https://img.shields.io/badge/Platform-Android-green.svg)](https://developer.android.com)
[![Kotlin](https://img.shields.io/badge/Language-Kotlin-blue.svg)](https://kotlinlang.org)
[![API](https://img.shields.io/badge/API-24%2B-brightgreen.svg)](https://android-arsenal.com/api?level=24)

一个集成了 **AI 语音识别**、**姿态检测**、**智能对话** 和 **音频播放** 的综合性运动健身应用。

## 🎯 **项目概述**

AI Sports 是一个基于 Android 平台的智能运动助手，结合了多种 AI 技术，为用户提供智能化的运动体验：

- 🎤 **语音识别** - 离线语音转文字
- 🤖 **智能对话** - 集成 MCP (Model Context Protocol) 服务器
- 🏃 **姿态检测** - 基于 YOLO11 的实时人体姿态识别
- 🎵 **音频播放** - WebSocket 推送音频的队列播放系统
- 🌐 **实时通信** - WebSocket + SSE 双向通信

## 🏗️ **技术架构**

### **模块化架构**
```
sport-ai/
├── app/                    # 主应用模块
├── library-common/         # 通用基础库
├── library-stt/           # 语音识别模块
├── library-pose/          # 姿态检测模块
└── library-mcp-server/    # MCP 服务器模块
```

### **技术栈**

#### **核心框架**
- **Kotlin** - 主要开发语言
- **Android Jetpack** - 现代 Android 开发组件
- **DataBinding** - 数据绑定
- **Coroutines** - 异步编程

#### **网络通信**
- **Ktor Client** - HTTP 客户端 + WebSocket 支持
- **OkHttp** - WebSocket 引擎
- **SSE (Server-Sent Events)** - 服务器推送

#### **AI & 机器学习**
- **Sherpa-NCNN** - 离线语音识别引擎
- **YOLO11 + NCNN** - 轻量级姿态检测
- **OpenCV** - 计算机视觉处理

#### **路由 & 导航**
- **ARouter** - 组件化路由框架
- **模块解耦** - 支持独立开发和测试

#### **音频处理**
- **MediaPlayer** - 音频播放
- **AudioRecord** - 音频录制
- **音频增强** - 降噪、回声消除、自动增益