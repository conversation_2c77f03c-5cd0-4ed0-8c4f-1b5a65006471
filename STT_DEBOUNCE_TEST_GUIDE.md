# 🧪 STT防抖和拼接测试指南

## 🎯 **测试目的**

验证STT的防抖机制和文本拼接功能是否正常工作。

## 🔧 **修复的问题**

### **问题1: 文本拼接逻辑错误**
```
❌ 修复前:
"我" → "我，想" → "我，想，开始" → "我，想，开始，跳绳"

✅ 修复后:
"我" → "我想" → "我想开始" → "我想开始跳绳"
```

### **问题2: 防抖逻辑混乱**
```
❌ 修复前:
每次新文本都取消防抖 → 防抖失效

✅ 修复后:
每次新文本重启防抖计时器 → 防抖正常工作
```

## 📋 **测试用例**

### **测试1: 基本拼接功能**
```
说话内容: "我想开始跳绳训练"

预期STT序列:
1. "我"
2. "我想"
3. "我想开始"
4. "我想开始跳绳"
5. "我想开始跳绳训练"

预期界面显示:
- 立即显示: "我"
- 实时更新: "我想"
- 实时更新: "我想开始"
- 实时更新: "我想开始跳绳"
- 实时更新: "我想开始跳绳训练"

预期日志:
STT 拼接: 初始文本 '我'
STT 实时: 收到新文本 '我'，当前拼接: '我'
STT 拼接: 扩展文本 '我' → '我想'
STT 实时: 收到新文本 '我想'，当前拼接: '我想'
STT 拼接: 扩展文本 '我想' → '我想开始'
STT 实时: 收到新文本 '我想开始'，当前拼接: '我想开始'
...
```

### **测试2: 防抖机制**
```
说话内容: "开始跳绳"（说完后停止1.5秒）

预期行为:
1. 收到STT文本 → 立即显示
2. 启动1.2秒防抖计时器
3. 1.2秒内没有新文本 → 发送AI请求
4. 显示AI回复

预期日志:
STT 实时: 收到新文本 '开始跳绳'，当前拼接: '开始跳绳'
（等待1.2秒）
STT 防抖完成: 处理完整文本 '开始跳绳'
STT 完成: 最终文本 '开始跳绳'
```

### **测试3: 防抖重启机制**
```
说话内容: "我想"（停顿0.5秒）"开始跳绳"

预期行为:
1. 收到"我想" → 显示 → 启动防抖
2. 0.5秒后收到"我想开始跳绳" → 更新显示 → 重启防抖
3. 1.2秒后 → 发送AI请求

预期日志:
STT 实时: 收到新文本 '我想'，当前拼接: '我想'
（0.5秒后）
STT 防抖: 任务被取消（收到新文本）
STT 实时: 收到新文本 '我想开始跳绳'，当前拼接: '我想开始跳绳'
（1.2秒后）
STT 防抖完成: 处理完整文本 '我想开始跳绳'
```

### **测试4: 独立片段拼接**
```
说话内容: "开始"（停顿）"跳绳"

预期行为:
如果STT返回两个独立片段，应该用空格拼接

预期结果:
"开始" + " " + "跳绳" = "开始 跳绳"

预期日志:
STT 拼接: 初始文本 '开始'
STT 拼接: 添加独立片段 '开始' + '跳绳'
```

## 🔍 **调试方法**

### **1. 观察日志输出**
```
关键日志标识:
- "STT 拼接:" - 文本拼接过程
- "STT 实时:" - 实时显示过程  
- "STT 防抖:" - 防抖机制状态
- "STT 完成:" - 最终处理结果
```

### **2. 检查界面显示**
```
验证点:
✅ 文本立即显示
✅ 文本实时更新
✅ 拼接结果正确
✅ 最终发送AI请求
```

### **3. 时间测试**
```
测试防抖时间:
1. 说话后立即停止
2. 计时1.2秒
3. 确认在1.2秒后发送AI请求
```

## 📊 **预期结果**

### **正常工作的标志**
```
✅ 文本拼接正确:
- 扩展文本: "我" → "我想" → "我想开始跳绳"
- 独立片段: "开始" + "跳绳" = "开始 跳绳"

✅ 防抖机制正常:
- 连续说话时不发送请求
- 停止说话1.2秒后发送请求
- 新文本会重启防抖计时器

✅ 实时显示正常:
- 立即显示第一个文本
- 实时更新后续文本
- 界面响应流畅
```

### **异常情况处理**
```
✅ 重复文本忽略:
收到相同文本时不重复处理

✅ 空文本跳过:
防抖完成时如果文本为空则跳过

✅ 任务取消处理:
新文本到达时正确取消旧的防抖任务
```

## 🛠️ **故障排除**

### **如果拼接不正确**
```
检查项:
1. appendSttText 方法逻辑
2. STT返回的文本格式
3. 文本比较逻辑

解决方案:
1. 检查日志中的拼接过程
2. 调整文本比较算法
3. 添加更多调试信息
```

### **如果防抖不工作**
```
检查项:
1. sttDebounceJob 是否被正确取消/重启
2. 防抖延迟时间设置
3. 协程作用域是否正确

解决方案:
1. 观察防抖日志
2. 调整防抖时间
3. 检查协程生命周期
```

### **如果显示不及时**
```
检查项:
1. displayCurrentSttText 是否在主线程调用
2. RecyclerView 更新是否正常
3. 消息索引是否正确

解决方案:
1. 确保UI更新在主线程
2. 检查适配器更新逻辑
3. 验证消息索引管理
```

## ✅ **验证清单**

- [ ] 文本立即显示
- [ ] 文本正确拼接（扩展模式）
- [ ] 独立片段正确拼接（空格分隔）
- [ ] 防抖机制正常工作（1.2秒延迟）
- [ ] 防抖重启机制正常
- [ ] 重复文本被忽略
- [ ] 空文本被跳过
- [ ] 最终AI请求正确发送
- [ ] 界面响应流畅
- [ ] 日志输出清晰

## 🎯 **最佳测试实践**

### **1. 分步测试**
- 先测试基本拼接
- 再测试防抖机制
- 最后测试复杂场景

### **2. 多种语速测试**
- 正常语速
- 快速说话
- 慢速说话
- 有停顿的说话

### **3. 边界情况测试**
- 单个字
- 长句子
- 特殊符号
- 数字和英文

通过这些测试，可以确保STT的防抖和拼接功能正常工作！
