# 🧪 热词效果测试指南

## 🎯 **测试目的**

验证热词功能是否生效，以及识别准确率的提升程度。

## 📋 **测试准备**

### **1. 准备测试语音**
选择容易被误识别的词汇进行测试：

```
🎯 核心测试词汇：
- "开始跳绳" (容易识别为: 跳神、跳声、跳生)
- "结束跳绳" (容易识别为: 结束跳神)
- "AI助手" (容易识别为: 爱助手、哎助手)
- "运动建议" (容易识别为: 运动建立)
- "健康状态" (容易识别为: 健康状况)

📝 测试句子：
- "我想开始跳绳训练"
- "请给我一些运动建议"
- "查看我的健康状态"
- "AI助手帮我启动跳绳"
- "结束跳绳运动"
```

### **2. 测试环境**
```
✅ 理想环境：
- 安静的室内环境
- 距离麦克风30-50cm
- 清晰的发音
- 正常语速

❌ 避免：
- 嘈杂环境
- 距离过远或过近
- 模糊发音
- 过快或过慢语速
```

## 🔬 **测试方法**

### **方法1: A/B对比测试**

#### **步骤1: 禁用热词测试**
```kotlin
// 临时禁用热词
val config = RecognizerConfig(
    // ... 其他配置
    hotwordsFile = "",      // 禁用热词
    hotwordsScore = 1.0f,
)
```

#### **步骤2: 记录识别结果**
```
测试语音: "开始跳绳训练"
重复测试10次，记录结果：

无热词结果：
1. "开始跳神训练" ❌
2. "开始跳绳训练" ✅
3. "开始跳声训练" ❌
4. "开始跳绳训练" ✅
5. "开始跳生训练" ❌
...

准确率: 4/10 = 40%
```

#### **步骤3: 启用热词测试**
```kotlin
// 启用热词
val config = RecognizerConfig(
    // ... 其他配置
    hotwordsFile = "hotwords.txt",
    hotwordsScore = 1.8f,
)
```

#### **步骤4: 对比结果**
```
测试语音: "开始跳绳训练"
重复测试10次，记录结果：

有热词结果：
1. "开始跳绳训练" ✅
2. "开始跳绳训练" ✅
3. "开始跳绳训练" ✅
4. "开始跳绳训练" ✅
5. "开始跳绳训练" ✅
...

准确率: 9/10 = 90%
提升: 90% - 40% = 50%
```

### **方法2: 日志监控测试**

#### **查看初始化日志**
```
启动应用时查看日志：

✅ 热词启用成功：
🎯 热词配置: 文件=hotwords.txt, 权重=1.8
✅ 热词功能已启用，将提高特定词汇识别准确率

❌ 热词未启用：
🎯 热词配置: 文件=, 权重=1.0
❌ 热词功能未启用
```

#### **监控识别结果**
```
测试时观察日志输出：

有热词时：
stt---开始跳绳训练 ✅
stt---开始跳绳训练 ✅
stt---开始跳绳训练 ✅

无热词时：
stt---开始跳神训练 ❌
stt---开始跳绳训练 ✅
stt---开始跳声训练 ❌
```

### **方法3: 实际使用测试**

#### **功能调用测试**
```
测试热词是否影响功能调用：

说话: "开始跳绳"
期望: 成功打开跳绳界面

无热词时可能：
识别为: "开始跳神" → 功能调用失败 ❌

有热词时应该：
识别为: "开始跳绳" → 功能调用成功 ✅
```

## 📊 **测试记录表**

### **识别准确率测试表**
```
| 测试词汇 | 无热词准确率 | 有热词准确率 | 提升幅度 |
|----------|-------------|-------------|----------|
| 开始跳绳 | 40%         | 90%         | +50%     |
| 结束跳绳 | 35%         | 85%         | +50%     |
| AI助手   | 30%         | 95%         | +65%     |
| 运动建议 | 60%         | 85%         | +25%     |
| 健康状态 | 70%         | 90%         | +20%     |
```

### **功能调用成功率测试**
```
| 功能指令 | 无热词成功率 | 有热词成功率 | 提升幅度 |
|----------|-------------|-------------|----------|
| 跳绳启动 | 40%         | 90%         | +50%     |
| 跳绳结束 | 35%         | 85%         | +50%     |
| 获取建议 | 60%         | 85%         | +25%     |
| 状态查询 | 70%         | 90%         | +20%     |
```

## 🔍 **问题排查**

### **热词没有生效的可能原因**

#### **1. 文件问题**
```
检查项：
- hotwords.txt 文件是否存在于 assets 目录
- 文件格式是否正确 (UTF-8编码)
- 词汇和权重格式是否正确

解决方案：
- 确认文件路径: app/src/main/assets/hotwords.txt
- 检查文件内容格式: "词汇 权重"
- 重新编译应用
```

#### **2. 配置问题**
```
检查项：
- RecognizerConfig 中是否正确设置 hotwordsFile
- hotwordsScore 是否在合理范围 (0.5-3.0)
- 是否重新初始化了 SttHelper

解决方案：
- 确认配置: hotwordsFile = "hotwords.txt"
- 调整权重: hotwordsScore = 1.8f
- 重启应用让配置生效
```

#### **3. 权重问题**
```
检查项：
- 权重是否过低 (<1.0)
- 权重是否过高 (>3.0)
- 是否与其他词汇权重冲突

解决方案：
- 适当提高权重 (1.5-2.5)
- 避免极端权重值
- 测试不同权重的效果
```

## ✅ **验证成功的标志**

### **1. 日志确认**
```
✅ 看到以下日志表示热词已启用：
🎯 热词配置: 文件=hotwords.txt, 权重=1.8
✅ 热词功能已启用，将提高特定词汇识别准确率
```

### **2. 识别准确率提升**
```
✅ 核心词汇识别准确率显著提升：
- "开始跳绳": 从40%提升到90%
- "AI助手": 从30%提升到95%
- "运动建议": 从60%提升到85%
```

### **3. 功能调用成功率提升**
```
✅ 语音指令功能调用更加可靠：
- 跳绳启动指令成功率提升50%
- 其他功能指令成功率普遍提升20-50%
```

## 🎯 **最佳测试实践**

### **1. 系统性测试**
- 测试所有热词文件中的词汇
- 每个词汇至少测试10次
- 记录详细的测试数据

### **2. 多环境测试**
- 安静环境 vs 嘈杂环境
- 不同距离 (近距离 vs 远距离)
- 不同发音 (清晰 vs 模糊)

### **3. 长期监控**
- 收集用户实际使用数据
- 定期分析识别错误模式
- 根据数据调整热词配置

通过这些测试方法，你可以清楚地验证热词功能是否生效，以及具体的改善程度！
