// Layer Shader Enum header
//
// This file is auto-generated by cmake, don't edit it.

absval = 0,
absval_pack4 = 1,
absval_pack8 = 2,
batchnorm = 3,
batchnorm_pack4 = 4,
batchnorm_pack8 = 5,
concat = 6,
concat_pack4 = 7,
concat_pack4to1 = 8,
concat_pack8 = 9,
concat_pack8to1 = 10,
concat_pack8to4 = 11,
convolution = 12,
convolution_1x1s1d1 = 13,
convolution_3x3s1d1_winograd23_transform_input = 14,
convolution_3x3s1d1_winograd23_transform_output = 15,
convolution_3x3s1d1_winograd43_transform_input = 16,
convolution_3x3s1d1_winograd43_transform_output = 17,
convolution_3x3s1d1_winograd_gemm = 18,
convolution_gemm = 19,
convolution_pack1to4 = 20,
convolution_pack1to4_1x1s1d1 = 21,
convolution_pack1to4_3x3s1d1_winograd_gemm = 22,
convolution_pack1to4_gemm = 23,
convolution_pack1to8 = 24,
convolution_pack1to8_1x1s1d1 = 25,
convolution_pack1to8_3x3s1d1_winograd_gemm = 26,
convolution_pack1to8_gemm = 27,
convolution_pack4 = 28,
convolution_pack4_1x1s1d1 = 29,
convolution_pack4_1x1s1d1_khr_cm_16_16_16 = 30,
convolution_pack4_1x1s1d1_khr_cm_16_8_8 = 31,
convolution_pack4_1x1s1d1_nv_cm_16_16_16 = 32,
convolution_pack4_1x1s1d1_nv_cm_16_8_8 = 33,
convolution_pack4_3x3s1d1_winograd23_transform_input = 34,
convolution_pack4_3x3s1d1_winograd23_transform_output = 35,
convolution_pack4_3x3s1d1_winograd43_transform_input = 36,
convolution_pack4_3x3s1d1_winograd43_transform_output = 37,
convolution_pack4_3x3s1d1_winograd_gemm = 38,
convolution_pack4_3x3s1d1_winograd_gemm_khr_cm_16_16_16 = 39,
convolution_pack4_3x3s1d1_winograd_gemm_khr_cm_16_8_8 = 40,
convolution_pack4_3x3s1d1_winograd_gemm_nv_cm_16_16_16 = 41,
convolution_pack4_3x3s1d1_winograd_gemm_nv_cm_16_8_8 = 42,
convolution_pack4_gemm = 43,
convolution_pack4_gemm_khr_cm_16_16_16 = 44,
convolution_pack4_gemm_khr_cm_16_8_8 = 45,
convolution_pack4_gemm_nv_cm_16_16_16 = 46,
convolution_pack4_gemm_nv_cm_16_8_8 = 47,
convolution_pack4to1 = 48,
convolution_pack4to1_1x1s1d1 = 49,
convolution_pack4to1_3x3s1d1_winograd_gemm = 50,
convolution_pack4to1_gemm = 51,
convolution_pack4to8 = 52,
convolution_pack4to8_1x1s1d1 = 53,
convolution_pack4to8_3x3s1d1_winograd_gemm = 54,
convolution_pack4to8_gemm = 55,
convolution_pack8 = 56,
convolution_pack8_1x1s1d1 = 57,
convolution_pack8_3x3s1d1_winograd23_transform_input = 58,
convolution_pack8_3x3s1d1_winograd23_transform_output = 59,
convolution_pack8_3x3s1d1_winograd43_transform_input = 60,
convolution_pack8_3x3s1d1_winograd43_transform_output = 61,
convolution_pack8_3x3s1d1_winograd_gemm = 62,
convolution_pack8_gemm = 63,
convolution_pack8to1 = 64,
convolution_pack8to1_1x1s1d1 = 65,
convolution_pack8to1_3x3s1d1_winograd_gemm = 66,
convolution_pack8to1_gemm = 67,
convolution_pack8to4 = 68,
convolution_pack8to4_1x1s1d1 = 69,
convolution_pack8to4_3x3s1d1_winograd_gemm = 70,
convolution_pack8to4_gemm = 71,
crop = 72,
crop_pack1to4 = 73,
crop_pack1to8 = 74,
crop_pack4 = 75,
crop_pack4to1 = 76,
crop_pack4to8 = 77,
crop_pack8 = 78,
crop_pack8to1 = 79,
crop_pack8to4 = 80,
deconvolution = 81,
deconvolution_col2im = 82,
deconvolution_gemm = 83,
deconvolution_pack1to4 = 84,
deconvolution_pack1to4_gemm = 85,
deconvolution_pack1to8 = 86,
deconvolution_pack1to8_gemm = 87,
deconvolution_pack4 = 88,
deconvolution_pack4_col2im = 89,
deconvolution_pack4_gemm = 90,
deconvolution_pack4_gemm_khr_cm_16_16_16 = 91,
deconvolution_pack4_gemm_khr_cm_16_8_8 = 92,
deconvolution_pack4_gemm_nv_cm_16_16_16 = 93,
deconvolution_pack4_gemm_nv_cm_16_8_8 = 94,
deconvolution_pack4to1 = 95,
deconvolution_pack4to1_gemm = 96,
deconvolution_pack4to8 = 97,
deconvolution_pack4to8_gemm = 98,
deconvolution_pack8 = 99,
deconvolution_pack8_col2im = 100,
deconvolution_pack8_gemm = 101,
deconvolution_pack8to1 = 102,
deconvolution_pack8to1_gemm = 103,
deconvolution_pack8to4 = 104,
deconvolution_pack8to4_gemm = 105,
dropout = 106,
dropout_pack4 = 107,
dropout_pack8 = 108,
eltwise = 109,
eltwise_pack4 = 110,
eltwise_pack8 = 111,
elu = 112,
elu_pack4 = 113,
elu_pack8 = 114,
flatten = 115,
flatten_pack1to4 = 116,
flatten_pack1to8 = 117,
flatten_pack4 = 118,
flatten_pack4to8 = 119,
flatten_pack8 = 120,
innerproduct = 121,
innerproduct_gemm = 122,
innerproduct_gemm_wp1to4 = 123,
innerproduct_gemm_wp1to8 = 124,
innerproduct_gemm_wp4 = 125,
innerproduct_gemm_wp4to1 = 126,
innerproduct_gemm_wp4to8 = 127,
innerproduct_gemm_wp8 = 128,
innerproduct_gemm_wp8to1 = 129,
innerproduct_gemm_wp8to4 = 130,
innerproduct_pack1to4 = 131,
innerproduct_pack1to8 = 132,
innerproduct_pack4 = 133,
innerproduct_pack4to1 = 134,
innerproduct_pack4to8 = 135,
innerproduct_pack8 = 136,
innerproduct_pack8to1 = 137,
innerproduct_pack8to4 = 138,
innerproduct_reduce_sum8 = 139,
innerproduct_reduce_sum8_pack4 = 140,
innerproduct_reduce_sum8_pack8 = 141,
innerproduct_sum8 = 142,
innerproduct_sum8_pack1to4 = 143,
innerproduct_sum8_pack1to8 = 144,
innerproduct_sum8_pack4 = 145,
innerproduct_sum8_pack4to1 = 146,
innerproduct_sum8_pack4to8 = 147,
innerproduct_sum8_pack8 = 148,
innerproduct_sum8_pack8to1 = 149,
innerproduct_sum8_pack8to4 = 150,
lrn_norm = 151,
lrn_norm_across_channel_pack4 = 152,
lrn_norm_across_channel_pack8 = 153,
lrn_norm_within_channel_pack4 = 154,
lrn_norm_within_channel_pack8 = 155,
lrn_square_pad = 156,
lrn_square_pad_across_channel_pack4 = 157,
lrn_square_pad_across_channel_pack8 = 158,
lrn_square_pad_within_channel_pack4 = 159,
lrn_square_pad_within_channel_pack8 = 160,
pooling = 161,
pooling_adaptive = 162,
pooling_adaptive_pack4 = 163,
pooling_adaptive_pack8 = 164,
pooling_global_reduce_max = 165,
pooling_global_reduce_max_first = 166,
pooling_global_reduce_max_first_pack4 = 167,
pooling_global_reduce_max_first_pack8 = 168,
pooling_global_reduce_max_last = 169,
pooling_global_reduce_max_last_pack4 = 170,
pooling_global_reduce_max_last_pack8 = 171,
pooling_global_reduce_max_pack4 = 172,
pooling_global_reduce_max_pack8 = 173,
pooling_global_reduce_sum = 174,
pooling_global_reduce_sum_first = 175,
pooling_global_reduce_sum_first_pack4 = 176,
pooling_global_reduce_sum_first_pack8 = 177,
pooling_global_reduce_sum_last = 178,
pooling_global_reduce_sum_last_pack4 = 179,
pooling_global_reduce_sum_last_pack8 = 180,
pooling_global_reduce_sum_pack4 = 181,
pooling_global_reduce_sum_pack8 = 182,
pooling_pack4 = 183,
pooling_pack8 = 184,
prelu = 185,
prelu_pack4 = 186,
prelu_pack8 = 187,
relu = 188,
relu_pack4 = 189,
relu_pack8 = 190,
reshape = 191,
reshape_pack1to4 = 192,
reshape_pack1to8 = 193,
reshape_pack4 = 194,
reshape_pack4to1 = 195,
reshape_pack4to8 = 196,
reshape_pack8 = 197,
reshape_pack8to1 = 198,
reshape_pack8to4 = 199,
scale = 200,
scale_pack4 = 201,
scale_pack8 = 202,
sigmoid = 203,
sigmoid_pack4 = 204,
sigmoid_pack8 = 205,
slice = 206,
slice_pack1to4 = 207,
slice_pack1to8 = 208,
slice_pack4 = 209,
slice_pack4to8 = 210,
slice_pack8 = 211,
softmax_div_sum = 212,
softmax_div_sum_pack4 = 213,
softmax_div_sum_pack8 = 214,
softmax_exp_sub_max = 215,
softmax_exp_sub_max_pack4 = 216,
softmax_exp_sub_max_pack8 = 217,
softmax_reduce_max = 218,
softmax_reduce_max_pack4 = 219,
softmax_reduce_max_pack8 = 220,
softmax_reduce_sum = 221,
softmax_reduce_sum_pack4 = 222,
softmax_reduce_sum_pack8 = 223,
tanh = 224,
tanh_pack4 = 225,
tanh_pack8 = 226,
binaryop = 227,
binaryop_broadcast = 228,
binaryop_broadcast_pack1to4 = 229,
binaryop_broadcast_pack1to8 = 230,
binaryop_broadcast_pack4 = 231,
binaryop_broadcast_pack8 = 232,
binaryop_pack4 = 233,
binaryop_pack8 = 234,
unaryop = 235,
unaryop_pack4 = 236,
unaryop_pack8 = 237,
convolutiondepthwise = 238,
convolutiondepthwise_group = 239,
convolutiondepthwise_group_pack1to4 = 240,
convolutiondepthwise_group_pack1to8 = 241,
convolutiondepthwise_group_pack4 = 242,
convolutiondepthwise_group_pack4to1 = 243,
convolutiondepthwise_group_pack4to8 = 244,
convolutiondepthwise_group_pack8 = 245,
convolutiondepthwise_group_pack8to1 = 246,
convolutiondepthwise_group_pack8to4 = 247,
convolutiondepthwise_pack4 = 248,
convolutiondepthwise_pack8 = 249,
padding = 250,
padding_3d = 251,
padding_3d_pack4 = 252,
padding_3d_pack8 = 253,
padding_pack1to4 = 254,
padding_pack1to8 = 255,
padding_pack4 = 256,
padding_pack4to1 = 257,
padding_pack4to8 = 258,
padding_pack8 = 259,
padding_pack8to1 = 260,
padding_pack8to4 = 261,
normalize_coeffs = 262,
normalize_coeffs_pack4 = 263,
normalize_coeffs_pack8 = 264,
normalize_norm = 265,
normalize_norm_pack4 = 266,
normalize_norm_pack8 = 267,
normalize_reduce_sum4_fp16_to_fp32 = 268,
normalize_reduce_sum4_fp16_to_fp32_pack4 = 269,
normalize_reduce_sum4_fp16_to_fp32_pack8 = 270,
normalize_reduce_sum4_fp32 = 271,
normalize_reduce_sum4_fp32_pack4 = 272,
normalize_reduce_sum4_fp32_pack8 = 273,
permute = 274,
permute_pack1to4 = 275,
permute_pack1to8 = 276,
permute_pack4 = 277,
permute_pack4to1 = 278,
permute_pack4to8 = 279,
permute_pack8 = 280,
permute_pack8to1 = 281,
permute_pack8to4 = 282,
priorbox = 283,
priorbox_mxnet = 284,
interp = 285,
interp_bicubic = 286,
interp_bicubic_coeffs = 287,
interp_bicubic_pack4 = 288,
interp_bicubic_pack8 = 289,
interp_pack4 = 290,
interp_pack8 = 291,
deconvolutiondepthwise = 292,
deconvolutiondepthwise_group = 293,
deconvolutiondepthwise_group_pack1to4 = 294,
deconvolutiondepthwise_group_pack1to8 = 295,
deconvolutiondepthwise_group_pack4 = 296,
deconvolutiondepthwise_group_pack4to1 = 297,
deconvolutiondepthwise_group_pack4to8 = 298,
deconvolutiondepthwise_group_pack8 = 299,
deconvolutiondepthwise_group_pack8to1 = 300,
deconvolutiondepthwise_group_pack8to4 = 301,
deconvolutiondepthwise_pack4 = 302,
deconvolutiondepthwise_pack8 = 303,
shufflechannel = 304,
shufflechannel_pack4 = 305,
shufflechannel_pack8 = 306,
instancenorm_coeffs = 307,
instancenorm_coeffs_pack4 = 308,
instancenorm_coeffs_pack8 = 309,
instancenorm_norm = 310,
instancenorm_norm_pack4 = 311,
instancenorm_norm_pack8 = 312,
instancenorm_reduce_mean = 313,
instancenorm_reduce_mean_pack4 = 314,
instancenorm_reduce_mean_pack8 = 315,
instancenorm_reduce_sum4_fp16_to_fp32 = 316,
instancenorm_reduce_sum4_fp16_to_fp32_pack4 = 317,
instancenorm_reduce_sum4_fp16_to_fp32_pack8 = 318,
instancenorm_reduce_sum4_fp32 = 319,
instancenorm_reduce_sum4_fp32_pack4 = 320,
instancenorm_reduce_sum4_fp32_pack8 = 321,
instancenorm_sub_mean_square = 322,
instancenorm_sub_mean_square_pack4 = 323,
instancenorm_sub_mean_square_pack8 = 324,
clip = 325,
clip_pack4 = 326,
clip_pack8 = 327,
reorg = 328,
reorg_pack1to4 = 329,
reorg_pack1to8 = 330,
reorg_pack4 = 331,
reorg_pack4to8 = 332,
reorg_pack8 = 333,
packing = 334,
packing_fp16_to_fp32 = 335,
packing_fp32_to_fp16 = 336,
packing_pack1to4 = 337,
packing_pack1to4_fp16_to_fp32 = 338,
packing_pack1to4_fp32_to_fp16 = 339,
packing_pack1to8 = 340,
packing_pack1to8_fp16_to_fp32 = 341,
packing_pack1to8_fp32_to_fp16 = 342,
packing_pack4 = 343,
packing_pack4_fp16_to_fp32 = 344,
packing_pack4_fp32_to_fp16 = 345,
packing_pack4to1 = 346,
packing_pack4to1_fp16_to_fp32 = 347,
packing_pack4to1_fp32_to_fp16 = 348,
packing_pack4to8 = 349,
packing_pack4to8_fp16_to_fp32 = 350,
packing_pack4to8_fp32_to_fp16 = 351,
packing_pack8 = 352,
packing_pack8_fp16_to_fp32 = 353,
packing_pack8_fp32_to_fp16 = 354,
packing_pack8to1 = 355,
packing_pack8to1_fp16_to_fp32 = 356,
packing_pack8to1_fp32_to_fp16 = 357,
packing_pack8to4 = 358,
packing_pack8to4_fp16_to_fp32 = 359,
packing_pack8to4_fp32_to_fp16 = 360,
cast_fp16_to_fp32 = 361,
cast_fp16_to_fp32_pack4 = 362,
cast_fp16_to_fp32_pack8 = 363,
cast_fp32_to_fp16 = 364,
cast_fp32_to_fp16_pack4 = 365,
cast_fp32_to_fp16_pack8 = 366,
hardsigmoid = 367,
hardsigmoid_pack4 = 368,
hardsigmoid_pack8 = 369,
hardswish = 370,
hardswish_pack4 = 371,
hardswish_pack8 = 372,
pixelshuffle = 373,
pixelshuffle_pack4 = 374,
pixelshuffle_pack4to1 = 375,
pixelshuffle_pack8 = 376,
pixelshuffle_pack8to1 = 377,
pixelshuffle_pack8to4 = 378,
deepcopy = 379,
deepcopy_pack4 = 380,
deepcopy_pack8 = 381,
mish = 382,
mish_pack4 = 383,
mish_pack8 = 384,
swish = 385,
swish_pack4 = 386,
swish_pack8 = 387,
gemm = 388,
multiheadattention_qk_cross = 389,
multiheadattention_qk_cross_pack1to4 = 390,
multiheadattention_qk_cross_pack4 = 391,
multiheadattention_qk_cross_pack4to1 = 392,
multiheadattention_qkv_cross = 393,
multiheadattention_qkv_cross_pack1to4 = 394,
multiheadattention_qkv_cross_pack4 = 395,
multiheadattention_qkv_cross_pack4to1 = 396,
gelu = 397,
gelu_pack4 = 398,
gelu_pack8 = 399,
convolution1d = 400,
convolution1d_pack1to4 = 401,
convolution1d_pack1to8 = 402,
convolution1d_pack4 = 403,
convolution1d_pack4to1 = 404,
convolution1d_pack4to8 = 405,
convolution1d_pack8 = 406,
convolution1d_pack8to1 = 407,
convolution1d_pack8to4 = 408,
erf = 409,
erf_pack4 = 410,
erf_pack8 = 411,
celu = 412,
celu_pack4 = 413,
celu_pack8 = 414,
convert_ycbcr = 415,
vulkan_activation = 416,

