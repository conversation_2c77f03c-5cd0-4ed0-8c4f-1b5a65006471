// <PERSON><PERSON> is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2025 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include <android/asset_manager_jni.h>
#include <android/native_window_jni.h>
#include <android/native_window.h>

#include <android/log.h>

#include <jni.h>

#include <string>
#include <vector>

#include <platform.h>
#include <benchmark.h>

#include "yolo11.h"

#include "ndkcamera.h"

#include <opencv2/core/core.hpp>
#include <opencv2/imgproc/imgproc.hpp>

#if __ARM_NEON
#include <arm_neon.h>
#endif // __ARM_NEON

// 添加全局变量存储回调对象
static jobject g_callback_obj = NULL;
static jmethodID g_callback_mid = NULL;
static JavaVM* g_jvm = NULL;

static int draw_unsupported(cv::Mat& rgb)
{
    const char text[] = "unsupported";

    int baseLine = 0;
    cv::Size label_size = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, 1.0, 1, &baseLine);

    int y = (rgb.rows - label_size.height) / 2;
    int x = (rgb.cols - label_size.width) / 2;

    cv::rectangle(rgb, cv::Rect(cv::Point(x, y), cv::Size(label_size.width, label_size.height + baseLine)),
                    cv::Scalar(255, 255, 255), -1);

    cv::putText(rgb, text, cv::Point(x, y + label_size.height),
                cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 0, 0));

    return 0;
}

static int draw_fps(cv::Mat& rgb)
{
    // resolve moving average
    float avg_fps = 0.f;
    {
        static double t0 = 0.f;
        static float fps_history[10] = {0.f};

        double t1 = ncnn::get_current_time();
        if (t0 == 0.f)
        {
            t0 = t1;
            return 0;
        }

        float fps = 1000.f / (t1 - t0);
        t0 = t1;

        for (int i = 9; i >= 1; i--)
        {
            fps_history[i] = fps_history[i - 1];
        }
        fps_history[0] = fps;

        if (fps_history[9] == 0.f)
        {
            return 0;
        }

        for (int i = 0; i < 10; i++)
        {
            avg_fps += fps_history[i];
        }
        avg_fps /= 10.f;
    }

    char text[32];
    sprintf(text, "FPS=%.2f", avg_fps);

    int baseLine = 0;
    cv::Size label_size = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);

    int y = 0;
    int x = rgb.cols - label_size.width;

    cv::rectangle(rgb, cv::Rect(cv::Point(x, y), cv::Size(label_size.width, label_size.height + baseLine)),
                    cv::Scalar(255, 255, 255), -1);

    cv::putText(rgb, text, cv::Point(x, y + label_size.height),
                cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));

    return 0;
}

static YOLO11* g_yolo11 = 0;
static ncnn::Mutex lock;


// 调用回调的辅助函数
static void call_pose_callback(const std::vector<Object>& objects, int img_width, int img_height)
{
    if (g_callback_obj == NULL || g_callback_mid == NULL || g_jvm == NULL)
    {
        return;
    }

    JNIEnv* env = NULL;
    bool attached = false;

    // 获取JNIEnv
    int status = g_jvm->GetEnv((void**)&env, JNI_VERSION_1_4);
    if (status != JNI_OK)
    {
        status = g_jvm->AttachCurrentThread(&env, NULL);
        if (status != JNI_OK)
        {
            return;
        }
        attached = true;
    }

    // 计算需要的数组大小：每个人有17个关键点，每个关键点有x,y,prob三个值
    int personCount = objects.size();
    int arraySize = personCount * 17 * 3;

    // 创建float数组
    jfloatArray keypoints_array = env->NewFloatArray(arraySize);
    if (keypoints_array == NULL)
    {
        if (attached)
        {
            g_jvm->DetachCurrentThread();
        }
        return;
    }

    // 创建临时缓冲区
    float* buffer = new float[arraySize];

    // 存储每个人的关键点数据
    int index = 0;
    for (int i = 0; i < personCount; i++)
    {
        const Object& obj = objects[i];
        for (int j = 0; j < 17; j++) // 假设有17个关键点
        {
            if (j < obj.keypoints.size())
            {
                buffer[index++] = obj.keypoints[j].p.x;
                buffer[index++] = obj.keypoints[j].p.y;
                buffer[index++] = obj.keypoints[j].prob;
            }
            else
            {
                // 如果关键点不存在，填充0
                buffer[index++] = 0;
                buffer[index++] = 0;
                buffer[index++] = 0;
            }
        }
    }

    // 将数据复制到Java数组
    env->SetFloatArrayRegion(keypoints_array, 0, arraySize, buffer);

    // 调用回调方法
    env->CallVoidMethod(g_callback_obj, g_callback_mid, personCount, img_width, img_height, keypoints_array);

    // 释放临时缓冲区和局部引用
    delete[] buffer;
    env->DeleteLocalRef(keypoints_array);

    // 如果是附加的线程，分离它
    if (attached)
    {
        g_jvm->DetachCurrentThread();
    }
}

class MyNdkCamera : public NdkCameraWindow
{
public:
    virtual void on_image_render(cv::Mat& rgb) const;
};

void MyNdkCamera::on_image_render(cv::Mat& rgb) const
{
    // yolo11
    {
        ncnn::MutexLockGuard g(lock);

        if (g_yolo11)
        {
            std::vector<Object> objects;
            g_yolo11->detect(rgb, objects);

            // 调用回调函数，传递检测结果
            call_pose_callback(objects, rgb.cols, rgb.rows);

            g_yolo11->draw(rgb, objects);
        }
        else
        {
            draw_unsupported(rgb);
        }
    }

    draw_fps(rgb);
}

static MyNdkCamera* g_camera = 0;

extern "C" {

JNIEXPORT jint JNI_OnLoad(JavaVM* vm, void* reserved)
{
    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "JNI_OnLoad");

    g_camera = new MyNdkCamera;
    g_jvm = vm; // 保存JavaVM指针

    ncnn::create_gpu_instance();

    return JNI_VERSION_1_4;
}

JNIEXPORT void JNI_OnUnload(JavaVM* vm, void* reserved)
{
    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "JNI_OnUnload");

    {
        ncnn::MutexLockGuard g(lock);

        delete g_yolo11;
        g_yolo11 = 0;


        // 清理回调对象
        JNIEnv* env = NULL;
        if (g_jvm->GetEnv((void**)&env, JNI_VERSION_1_4) == JNI_OK && env != NULL)
        {
            if (g_callback_obj)
            {
                env->DeleteGlobalRef(g_callback_obj);
                g_callback_obj = NULL;
            }
        }
        g_callback_mid = NULL;
    }

    ncnn::destroy_gpu_instance();

    delete g_camera;
    g_camera = 0;
}

// 注册回调的JNI方法
JNIEXPORT jboolean JNICALL Java_com_haoxue_pose_YOLO11Ncnn_registerPoseCallback(JNIEnv* env, jobject thiz, jobject callback)
{
    ncnn::MutexLockGuard g(lock);

    // 清理旧的回调对象
    if (g_callback_obj)
    {
        env->DeleteGlobalRef(g_callback_obj);
        g_callback_obj = NULL;
    }

    if (callback == NULL)
    {
        return JNI_FALSE;
    }

    // 获取回调方法ID
    jclass callback_class = env->GetObjectClass(callback);
    if (callback_class == NULL)
    {
        return JNI_FALSE;
    }

    g_callback_mid = env->GetMethodID(callback_class, "onPoseDetected", "(III[F)V");
    if (g_callback_mid == NULL)
    {
        return JNI_FALSE;
    }

    // 创建全局引用
    g_callback_obj = env->NewGlobalRef(callback);

    return JNI_TRUE;
}

// 取消注册回调的JNI方法
JNIEXPORT jboolean JNICALL Java_com_haoxue_pose_YOLO11Ncnn_unregisterPoseCallback(JNIEnv* env, jobject thiz)
{
    ncnn::MutexLockGuard g(lock);

    if (g_callback_obj)
    {
        env->DeleteGlobalRef(g_callback_obj);
        g_callback_obj = NULL;
    }

    g_callback_mid = NULL;

    return JNI_TRUE;
}

// public native boolean loadModel(AssetManager mgr, int modelid, int cpugpu);
JNIEXPORT jboolean JNICALL Java_com_haoxue_pose_YOLO11Ncnn_loadModel(JNIEnv* env, jobject thiz, jobject assetManager, jint modelid, jint cpugpu)
{
    if (modelid < 0 || modelid > 8 || cpugpu < 0 || cpugpu > 2)
    {
        return JNI_FALSE;
    }

    AAssetManager* mgr = AAssetManager_fromJava(env, assetManager);

    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "loadModel %p", mgr);

    const char* modeltypes[9] =
    {
        "n",
        "s",
        "m",
        "n",
        "s",
        "m",
        "n",
        "s",
        "m"
    };

    std::string parampath = std::string("yolo11") + modeltypes[(int)modelid] + "_pose.ncnn.param";
    std::string modelpath = std::string("yolo11") + modeltypes[(int)modelid] + "_pose.ncnn.bin";
    bool use_gpu = (int)cpugpu == 1;
    bool use_turnip = (int)cpugpu == 2;

    {
        ncnn::MutexLockGuard g(lock);

        {
            static int old_modelid = 0;
            static int old_cpugpu = 0;
            if ((modelid % 3) != old_modelid || cpugpu != old_cpugpu)
            {
                delete g_yolo11;
                g_yolo11 = 0;
            }
            old_modelid = modelid % 3;
            old_cpugpu = cpugpu;

            ncnn::destroy_gpu_instance();

            if (use_turnip)
            {
                ncnn::create_gpu_instance("libvulkan_freedreno.so");
            }
            else if (use_gpu)
            {
                ncnn::create_gpu_instance();
            }

            if (!g_yolo11)
            {
                g_yolo11 = new YOLO11_pose;
                g_yolo11->load(mgr, parampath.c_str(), modelpath.c_str(), use_gpu || use_turnip);
            }
            int target_size = 320;
            if ((int)modelid >= 3)
                target_size = 480;
            if ((int)modelid >= 6)
                target_size = 640;
            g_yolo11->set_det_target_size(target_size);
        }
    }

    return JNI_TRUE;
}

// public native boolean openCamera(int facing);
JNIEXPORT jboolean JNICALL Java_com_haoxue_pose_YOLO11Ncnn_openCamera(JNIEnv* env, jobject thiz, jint facing)
{
    if (facing < 0 || facing > 1)
        return JNI_FALSE;

    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "openCamera %d", facing);

    g_camera->open((int)facing);

    return JNI_TRUE;
}

// public native boolean closeCamera();
JNIEXPORT jboolean JNICALL Java_com_haoxue_pose_YOLO11Ncnn_closeCamera(JNIEnv* env, jobject thiz)
{
    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "closeCamera");

    g_camera->close();

    return JNI_TRUE;
}

// public native boolean setOutputWindow(Surface surface);
JNIEXPORT jboolean JNICALL Java_com_haoxue_pose_YOLO11Ncnn_setOutputWindow(JNIEnv* env, jobject thiz, jobject surface)
{
    ANativeWindow* win = ANativeWindow_fromSurface(env, surface);

    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "setOutputWindow %p", win);

    g_camera->set_window(win);

    return JNI_TRUE;
}

}
