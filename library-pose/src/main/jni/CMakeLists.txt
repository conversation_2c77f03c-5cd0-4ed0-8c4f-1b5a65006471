# 添加编译优化
# set(CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS} -O3 -ffast-math -fno-rtti -fno-exceptions")
# 针对特定架构优化
# set(CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS} -march=armv8-a")
# 启用NEON指令集
# set(CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS} -mfpu=neon")

cmake_minimum_required(VERSION 3.10)

project(yolo11ncnn)

set(OpenCV_DIR ${CMAKE_SOURCE_DIR}/opencv-mobile-4.11.0-android/sdk/native/jni)
find_package(OpenCV REQUIRED core imgproc)

set(ncnn_DIR ${CMAKE_SOURCE_DIR}/${ANDROID_ABI}/lib/cmake/ncnn)
find_package(ncnn REQUIRED)

add_library(yolo11ncnn SHARED yolo11ncnn.cpp yolo11.cpp yolo11_pose.cpp ndkcamera.cpp)

target_link_libraries(yolo11ncnn ncnn ${OpenCV_LIBS} camera2ndk mediandk log)


