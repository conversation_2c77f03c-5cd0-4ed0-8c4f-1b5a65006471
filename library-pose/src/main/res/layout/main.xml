<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/buttonSwitchCamera"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="切换摄像头" />

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">

            <Spinner
                android:id="@+id/spinnerModel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawSelectorOnTop="true"
                android:entries="@array/model_array" />

            <Spinner
                android:id="@+id/spinnerCPUGPU"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawSelectorOnTop="true"
                android:entries="@array/cpugpu_array" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <SurfaceView
                android:id="@+id/cameraview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintLeft_toLeftOf="parent" />

            <TextView
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:id="@+id/textViewJumpCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_marginTop="10dp"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textViewJumpCount"
                android:id="@+id/jumpCountersLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            </LinearLayout>

            <Button
                android:text="重置检测"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:id="@+id/buttonResetCounter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>
