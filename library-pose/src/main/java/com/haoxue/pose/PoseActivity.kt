package com.haoxue.pose

import android.Manifest
import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.PixelFormat
import android.util.Log
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.View
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.AdapterView.OnItemSelectedListener
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Spinner
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.haoxue.libcommon.router.RouterPaths
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.pose.databinding.MainBinding
import com.tencent.yolo11ncnn.MotionManager
import com.tencent.yolo11ncnn.PoseDataCallback
import com.tencent.yolo11ncnn.SportType
import com.tencent.yolo11ncnn.YOLO11Ncnn

@Route(path = RouterPaths.Pose.DETECTION)
class PoseActivity : BaseActivity<MainBinding>(R.layout.main), SurfaceHolder.Callback,
    PoseDataCallback {
    private val yolo11ncnn = YOLO11Ncnn()

    // 动作管理器：根据当前运动类型分发计数逻辑
    private lateinit var motionManager: MotionManager

    private var facing = 1

    private var spinnerModel: Spinner? = null
    private var spinnerCPUGPU: Spinner? = null
    private var current_model = 0
    private var current_cpugpu = 0

    private var cameraView: SurfaceView? = null

    private var textViewJumpCount: TextView? = null
    private var jumpCountersLayout: LinearLayout? = null

    // 用于存储每个人的计数显示TextView
    private val personCountViews = mutableMapOf<Int, TextView>()


    override fun initCommonData() {
        window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
        setContentView(R.layout.main)

        cameraView = findViewById<View?>(R.id.cameraview) as SurfaceView

        jumpCountersLayout = findViewById<View?>(R.id.jumpCountersLayout) as LinearLayout

        cameraView!!.getHolder().setFormat(PixelFormat.RGBA_8888)
        cameraView!!.getHolder().addCallback(this)

        val buttonSwitchCamera = findViewById<View?>(R.id.buttonSwitchCamera) as Button
        buttonSwitchCamera.setOnClickListener(object : View.OnClickListener {
            override fun onClick(arg0: View?) {
                val new_facing = 1 - facing
                yolo11ncnn.closeCamera()
                yolo11ncnn.openCamera(new_facing)
                facing = new_facing
            }
        })

        val buttonResetCounter = findViewById<View?>(R.id.buttonResetCounter) as Button
        buttonResetCounter.setOnClickListener(object : View.OnClickListener {
            override fun onClick(arg0: View?) {
                jumpCountersLayout?.removeAllViews()
                personCountViews.clear()
                motionManager.resetCurrentCounter()
            }
        })


        spinnerModel = findViewById<View?>(R.id.spinnerModel) as Spinner
        spinnerModel!!.setOnItemSelectedListener(object : OnItemSelectedListener {
            override fun onItemSelected(
                arg0: AdapterView<*>?,
                arg1: View?,
                position: Int,
                id: Long
            ) {
                if (position != current_model) {
                    current_model = position
                    reload()
                }
            }

            override fun onNothingSelected(arg0: AdapterView<*>?) {
            }
        })

        spinnerCPUGPU = findViewById<View?>(R.id.spinnerCPUGPU) as Spinner
        spinnerCPUGPU!!.setOnItemSelectedListener(object : OnItemSelectedListener {
            override fun onItemSelected(
                arg0: AdapterView<*>?,
                arg1: View?,
                position: Int,
                id: Long
            ) {
                if (position != current_cpugpu) {
                    current_cpugpu = position
                    reload()
                }
            }

            override fun onNothingSelected(arg0: AdapterView<*>?) {
            }
        })
        reload()

        // 读取外部参数：运动类型与支持人数
        val intentSport = intent?.getStringExtra("SPORT_TYPE")
        val sportType = intentSport?.let { runCatching { SportType.valueOf(it) }.getOrNull() }
            ?: SportType.JUMP_ROPE
        val maxPerson = intent?.getIntExtra("MAX_PERSON", 1) ?: 1

        // 初始化 MotionManager
        motionManager = MotionManager({ counts ->
            runOnUiThread { updateJumpCountDisplay(counts) }
        }, sportType, maxPerson)
    }

    override fun initCommonListener() {
    }

    override fun requestCommonData() {
    }

    private fun reload() {
        val ret_init = yolo11ncnn.loadModel(assets, current_model, current_cpugpu)
        if (!ret_init) {
            Log.e("MainActivity", "yolo11ncnn loadModel failed")
        }
    }


    // 实现PoseDataCallback接口的回调方法
    @SuppressLint("SetTextI18n")
    override fun onPoseDetected(
        personCount: Int,
        imageWidth: Int,
        imageHeight: Int
    ) {
        val floatBuffer = yolo11ncnn.createOrGetSharedBuffer().asFloatBuffer()

        // 将帧数据交给 MotionManager
        motionManager.onFrame(personCount, imageWidth, imageHeight, floatBuffer)
    }

    // 更新每个人的跳绳计数显示
    private fun updateJumpCountDisplay(jumpInfos: Map<Int, Int>) {
        // 清除旧的视图
        jumpCountersLayout?.removeAllViews()
        personCountViews.clear()

        // 为每个人创建一个计数显示
        for ((personId, info) in jumpInfos) {
            val textView = TextView(this)
            textView.text = "第${personId + 1}个人: ${info}次"
            textView.setTextColor(Color.WHITE)
            textView.textSize = 22f
            textView.setPadding(0, 4, 0, 4)
            jumpCountersLayout?.addView(textView)
            personCountViews[personId] = textView
        }
    }


    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        yolo11ncnn.setOutputWindow(holder.getSurface())
    }

    override fun surfaceCreated(p0: SurfaceHolder) {

    }

    override fun surfaceDestroyed(p0: SurfaceHolder) {

    }

    public override fun onResume() {
        super.onResume()
        if (ContextCompat.checkSelfPermission(
                getApplicationContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_DENIED
        ) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf<String>(Manifest.permission.CAMERA),
                REQUEST_CAMERA
            )
        }
        // 初始化共享缓冲区（假设最多 5 个人）
        yolo11ncnn.createOrGetSharedBuffer(5)

        yolo11ncnn.registerPoseCallback(this)
        yolo11ncnn.openCamera(facing)
    }

    public override fun onPause() {
        super.onPause()
        // 取消注册回调
        yolo11ncnn.unregisterPoseCallback()
        yolo11ncnn.closeCamera()
    }

    companion object {
        const val REQUEST_CAMERA: Int = 100
    }
}
