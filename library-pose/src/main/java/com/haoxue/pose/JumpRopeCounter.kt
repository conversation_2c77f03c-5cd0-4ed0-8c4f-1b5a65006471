package com.haoxue.pose

class JumpRopeCounter {

    val map = mutableMapOf<Int, Int>()
    val mapJump = mutableMapOf<Int, CountData>()

    // 处理关键点数据并计算跳绳次数
    fun processKeyPointsData(
        personCount: Int,
        imageWidth: Int,
        imageHeight: Int,
        keypoints: FloatArray,
        callback: (num: Map<Int, Int>) -> Unit
    ) {

        if (keypoints.isEmpty()) return

        for (n in 0 until personCount) {
            if (mapJump[n] == null) {
                var jumpRopeCounter2 = CountData()
                mapJump[n] = jumpRopeCounter2
            }
            mapJump[n]!!.update(keypoints.copyOfRange(51 * n, 51 * (n + 1))) {
                map[n] = it
                callback(map)
            }
        }
    }

    fun reset() {
        mapJump.forEach {
            it.value.reset()
        }
        mapJump.clear()
    }
}