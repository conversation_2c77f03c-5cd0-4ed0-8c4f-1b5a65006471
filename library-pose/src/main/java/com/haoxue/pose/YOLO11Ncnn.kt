package com.haoxue.pose

import android.content.res.AssetManager
import android.view.Surface

class YOLO11Ncnn {
    external fun loadModel(mgr: AssetManager?, modelid: Int, cpugpu: Int): Boolean
    external fun openCamera(facing: Int): Boolean
    external fun closeCamera(): Boolean
    external fun setOutputWindow(surface: Surface?): Boolean

    external fun registerPoseCallback(callback: PoseDataCallback?): Boolean

    external fun unregisterPoseCallback(): Boolean
    companion object {
        init {
            System.loadLibrary("yolo11ncnn")
        }
    }
}
