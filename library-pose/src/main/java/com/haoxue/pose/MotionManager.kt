package com.tencent.yolo11ncnn

import com.haoxue.pose.JumpRopeCounter
import java.nio.FloatBuffer
import kotlin.math.min

/**
 * 负责在不同运动之间切换，并把每帧数据分发给当前运动 Counter。
 * UI 层只需要监听 [onCountUpdate] 回调即可。
 */
class MotionManager(
    private val onCountUpdate: (counts: Map<Int, Int>) -> Unit,
    defaultSport: SportType = SportType.JUMP_ROPE,
    defaultCapacity: Int = 1
) {

    private var currentCounter: MotionCounter = createCounter(defaultSport)
    private var currentSport: SportType = defaultSport
    private var personCapacity: Int = defaultCapacity

    private val tracker = PersonTracker()
    private val counts = mutableMapOf<Int, Int>()

    init {
        currentCounter.setMaxPerson(defaultCapacity)
    }

    fun switchTo(type: SportType, personCapacity: Int = 1) {
        if (type == currentSport && personCapacity == this.personCapacity) return
        currentCounter.release()
        currentCounter = createCounter(type)
        currentCounter.setMaxPerson(personCapacity)
        currentSport = type
        this.personCapacity = personCapacity
    }

    fun onFrame(personCount: Int, imageWidth: Int, imageHeight: Int, buffer: FloatBuffer) {
        if (personCount == 0) return

        val loopCount = min(personCount, personCapacity)
        buffer.position(0)

        val centers = mutableListOf<Pair<Float, Float>>()
        val personDataList = ArrayList<FloatArray>(loopCount)
        val temp = FloatArray(51)

        for (i in 0 until loopCount) {
            buffer.get(temp, 0, 51)
            val copy = temp.copyOf()
            personDataList.add(copy)
            val cx = (copy[11 * 3] + copy[12 * 3]) / 2f
            val cy = (copy[11 * 3 + 1] + copy[12 * 3 + 1]) / 2f
            centers.add(cx to cy)
        }

        val ids = tracker.assign(centers)

        var changed = false
        for (idx in 0 until loopCount) {
            val id = ids[idx]
            val newCount = currentCounter.processPerson(id, imageWidth, imageHeight, personDataList[idx])
            if (newCount != null) {
                counts[id] = newCount
                changed = true
            }
        }

        if (changed) onCountUpdate(counts.toMap())
    }

    fun resetCurrentCounter() {
        currentCounter.reset()
        counts.clear()
        tracker.reset()
    }

    fun release() {
        currentCounter.release()
    }

    private fun createCounter(type: SportType): MotionCounter = when (type) {
        SportType.JUMP_ROPE -> JumpRopeCounter()
        SportType.PULL_UP -> PullUpCounter()
        SportType.SIT_UP -> SitUpCounter()
    }
} 