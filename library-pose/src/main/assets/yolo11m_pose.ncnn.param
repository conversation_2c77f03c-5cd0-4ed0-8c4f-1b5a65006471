7767517
349 412
Input                    in0                      0 1 in0
Convolution              conv_1                   1 1 in0 1 0=64 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1728
Swish                    silu_118                 1 1 1 2
Convolution              conv_2                   1 1 2 3 0=128 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=73728
Swish                    silu_119                 1 1 3 4
Convolution              conv_3                   1 1 4 5 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_120                 1 1 5 6
Slice                    split_0                  1 2 6 7 8 -23300=2,64,64 1=0
Split                    splitncnn_0              1 3 8 9 10 11
Convolution              conv_4                   1 1 11 12 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2048
Swish                    silu_121                 1 1 12 13
Split                    splitncnn_1              1 2 13 14 15
Convolution              conv_5                   1 1 15 16 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Swish                    silu_122                 1 1 16 17
Convolution              conv_6                   1 1 17 18 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Swish                    silu_123                 1 1 18 19
BinaryOp                 add_0                    2 1 14 19 20 0=0
Split                    splitncnn_2              1 2 20 21 22
Convolution              conv_7                   1 1 22 23 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Swish                    silu_124                 1 1 23 24
Convolution              conv_8                   1 1 24 25 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Swish                    silu_125                 1 1 25 26
BinaryOp                 add_1                    2 1 21 26 27 0=0
Convolution              conv_9                   1 1 10 28 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2048
Swish                    silu_126                 1 1 28 29
Concat                   cat_0                    2 1 27 29 30 0=0
Convolution              conv_10                  1 1 30 31 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
Swish                    silu_127                 1 1 31 32
Concat                   cat_1                    3 1 7 9 32 33 0=0
Convolution              conv_11                  1 1 33 34 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=49152
Swish                    silu_128                 1 1 34 35
Convolution              conv_12                  1 1 35 36 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=589824
Swish                    silu_129                 1 1 36 37
Convolution              conv_13                  1 1 37 38 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_130                 1 1 38 39
Slice                    split_1                  1 2 39 40 41 -23300=2,128,128 1=0
Split                    splitncnn_3              1 3 41 42 43 44
Convolution              conv_14                  1 1 44 45 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Swish                    silu_131                 1 1 45 46
Split                    splitncnn_4              1 2 46 47 48
Convolution              conv_15                  1 1 48 49 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_132                 1 1 49 50
Convolution              conv_16                  1 1 50 51 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_133                 1 1 51 52
BinaryOp                 add_2                    2 1 47 52 53 0=0
Split                    splitncnn_5              1 2 53 54 55
Convolution              conv_17                  1 1 55 56 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_134                 1 1 56 57
Convolution              conv_18                  1 1 57 58 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_135                 1 1 58 59
BinaryOp                 add_3                    2 1 54 59 60 0=0
Convolution              conv_19                  1 1 43 61 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Swish                    silu_136                 1 1 61 62
Concat                   cat_2                    2 1 60 62 63 0=0
Convolution              conv_20                  1 1 63 64 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_137                 1 1 64 65
Concat                   cat_3                    3 1 40 42 65 66 0=0
Convolution              conv_21                  1 1 66 67 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=196608
Swish                    silu_138                 1 1 67 68
Split                    splitncnn_6              1 2 68 69 70
Convolution              conv_22                  1 1 70 71 0=512 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2359296
Swish                    silu_139                 1 1 71 72
Convolution              conv_23                  1 1 72 73 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_140                 1 1 73 74
Slice                    split_2                  1 2 74 75 76 -23300=2,256,256 1=0
Split                    splitncnn_7              1 3 76 77 78 79
Convolution              conv_24                  1 1 79 80 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_141                 1 1 80 81
Split                    splitncnn_8              1 2 81 82 83
Convolution              conv_25                  1 1 83 84 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_142                 1 1 84 85
Convolution              conv_26                  1 1 85 86 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_143                 1 1 86 87
BinaryOp                 add_4                    2 1 82 87 88 0=0
Split                    splitncnn_9              1 2 88 89 90
Convolution              conv_27                  1 1 90 91 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_144                 1 1 91 92
Convolution              conv_28                  1 1 92 93 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_145                 1 1 93 94
BinaryOp                 add_5                    2 1 89 94 95 0=0
Convolution              conv_29                  1 1 78 96 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_146                 1 1 96 97
Concat                   cat_4                    2 1 95 97 98 0=0
Convolution              conv_30                  1 1 98 99 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_147                 1 1 99 100
Concat                   cat_5                    3 1 75 77 100 101 0=0
Convolution              conv_31                  1 1 101 102 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_148                 1 1 102 103
Split                    splitncnn_10             1 2 103 104 105
Convolution              conv_32                  1 1 105 106 0=512 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2359296
Swish                    silu_149                 1 1 106 107
Convolution              conv_33                  1 1 107 108 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_150                 1 1 108 109
Slice                    split_3                  1 2 109 110 111 -23300=2,256,256 1=0
Split                    splitncnn_11             1 3 111 112 113 114
Convolution              conv_34                  1 1 114 115 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_151                 1 1 115 116
Split                    splitncnn_12             1 2 116 117 118
Convolution              conv_35                  1 1 118 119 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_152                 1 1 119 120
Convolution              conv_36                  1 1 120 121 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_153                 1 1 121 122
BinaryOp                 add_6                    2 1 117 122 123 0=0
Split                    splitncnn_13             1 2 123 124 125
Convolution              conv_37                  1 1 125 126 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_154                 1 1 126 127
Convolution              conv_38                  1 1 127 128 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_155                 1 1 128 129
BinaryOp                 add_7                    2 1 124 129 130 0=0
Convolution              conv_39                  1 1 113 131 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_156                 1 1 131 132
Concat                   cat_6                    2 1 130 132 133 0=0
Convolution              conv_40                  1 1 133 134 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_157                 1 1 134 135
Concat                   cat_7                    3 1 110 112 135 136 0=0
Convolution              conv_41                  1 1 136 137 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_158                 1 1 137 138
Convolution              conv_42                  1 1 138 139 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Swish                    silu_159                 1 1 139 140
Split                    splitncnn_14             1 2 140 141 142
Pooling                  maxpool2d_115            1 1 142 143 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_15             1 2 143 144 145
Pooling                  maxpool2d_116            1 1 145 146 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_16             1 2 146 147 148
Pooling                  maxpool2d_117            1 1 148 149 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Concat                   cat_8                    4 1 141 144 147 149 150 0=0
Convolution              conv_43                  1 1 150 151 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=524288
Swish                    silu_160                 1 1 151 152
Convolution              conv_44                  1 1 152 153 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_161                 1 1 153 154
Slice                    split_4                  1 2 154 155 156 -23300=2,256,256 1=0
Split                    splitncnn_17             1 2 156 157 158
Convolution              conv_45                  1 1 158 159 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Split                    splitncnn_18             1 3 159 160 161 162
Reshape                  view_228                 1 1 162 163 0=-1 1=128 2=4
Slice                    split_5                  1 3 163 164 165 166 -23300=3,32,32,64 1=1
Split                    splitncnn_19             1 2 166 167 168
Permute                  transpose_237            1 1 164 169 0=1
MatMul                   matmul_235               2 1 169 165 170
BinaryOp                 mul_8                    1 1 170 171 0=2 1=1 2=1.767770e-01
Softmax                  softmax_0                1 1 171 172 0=2 1=1
MatMul                   matmultransb_0           2 1 168 172 173 0=1
Reshape                  Tensor.view_6            2 1 173 160 174 6="1w,1h,256"
Reshape                  Tensor.reshape_4         2 1 167 161 175 6="1w,1h,256"
ConvolutionDepthWise     convdw_245               1 1 175 176 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
BinaryOp                 add_9                    2 1 174 176 177 0=0
Convolution              conv_46                  1 1 177 178 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
BinaryOp                 add_10                   2 1 157 178 179 0=0
Split                    splitncnn_20             1 2 179 180 181
Convolution              conv_47                  1 1 181 182 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Swish                    silu_162                 1 1 182 183
Convolution              conv_48                  1 1 183 184 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
BinaryOp                 add_11                   2 1 180 184 185 0=0
Concat                   cat_9                    2 1 155 185 186 0=0
Convolution              conv_49                  1 1 186 187 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_163                 1 1 187 188
Split                    splitncnn_21             1 2 188 189 190
Interp                   upsample_226             1 1 190 191 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_10                   2 1 191 104 192 0=0
Convolution              conv_50                  1 1 192 193 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=524288
Swish                    silu_164                 1 1 193 194
Slice                    split_6                  1 2 194 195 196 -23300=2,256,256 1=0
Split                    splitncnn_22             1 3 196 197 198 199
Convolution              conv_51                  1 1 199 200 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_165                 1 1 200 201
Split                    splitncnn_23             1 2 201 202 203
Convolution              conv_52                  1 1 203 204 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_166                 1 1 204 205
Convolution              conv_53                  1 1 205 206 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_167                 1 1 206 207
BinaryOp                 add_12                   2 1 202 207 208 0=0
Split                    splitncnn_24             1 2 208 209 210
Convolution              conv_54                  1 1 210 211 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_168                 1 1 211 212
Convolution              conv_55                  1 1 212 213 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_169                 1 1 213 214
BinaryOp                 add_13                   2 1 209 214 215 0=0
Convolution              conv_56                  1 1 198 216 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_170                 1 1 216 217
Concat                   cat_11                   2 1 215 217 218 0=0
Convolution              conv_57                  1 1 218 219 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_171                 1 1 219 220
Concat                   cat_12                   3 1 195 197 220 221 0=0
Convolution              conv_58                  1 1 221 222 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_172                 1 1 222 223
Split                    splitncnn_25             1 2 223 224 225
Interp                   upsample_227             1 1 225 226 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_13                   2 1 226 69 227 0=0
Convolution              conv_59                  1 1 227 228 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_173                 1 1 228 229
Slice                    split_7                  1 2 229 230 231 -23300=2,128,128 1=0
Split                    splitncnn_26             1 3 231 232 233 234
Convolution              conv_60                  1 1 234 235 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Swish                    silu_174                 1 1 235 236
Split                    splitncnn_27             1 2 236 237 238
Convolution              conv_61                  1 1 238 239 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_175                 1 1 239 240
Convolution              conv_62                  1 1 240 241 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_176                 1 1 241 242
BinaryOp                 add_14                   2 1 237 242 243 0=0
Split                    splitncnn_28             1 2 243 244 245
Convolution              conv_63                  1 1 245 246 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_177                 1 1 246 247
Convolution              conv_64                  1 1 247 248 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_178                 1 1 248 249
BinaryOp                 add_15                   2 1 244 249 250 0=0
Convolution              conv_65                  1 1 233 251 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Swish                    silu_179                 1 1 251 252
Concat                   cat_14                   2 1 250 252 253 0=0
Convolution              conv_66                  1 1 253 254 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_180                 1 1 254 255
Concat                   cat_15                   3 1 230 232 255 256 0=0
Convolution              conv_67                  1 1 256 257 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=98304
Swish                    silu_181                 1 1 257 258
Split                    splitncnn_29             1 4 258 259 260 261 262
Convolution              conv_68                  1 1 261 263 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=589824
Swish                    silu_182                 1 1 263 264
Concat                   cat_16                   2 1 264 224 265 0=0
Convolution              conv_69                  1 1 265 266 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_183                 1 1 266 267
Slice                    split_8                  1 2 267 268 269 -23300=2,256,256 1=0
Split                    splitncnn_30             1 3 269 270 271 272
Convolution              conv_70                  1 1 272 273 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_184                 1 1 273 274
Split                    splitncnn_31             1 2 274 275 276
Convolution              conv_71                  1 1 276 277 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_185                 1 1 277 278
Convolution              conv_72                  1 1 278 279 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_186                 1 1 279 280
BinaryOp                 add_16                   2 1 275 280 281 0=0
Split                    splitncnn_32             1 2 281 282 283
Convolution              conv_73                  1 1 283 284 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_187                 1 1 284 285
Convolution              conv_74                  1 1 285 286 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_188                 1 1 286 287
BinaryOp                 add_17                   2 1 282 287 288 0=0
Convolution              conv_75                  1 1 271 289 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_189                 1 1 289 290
Concat                   cat_17                   2 1 288 290 291 0=0
Convolution              conv_76                  1 1 291 292 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_190                 1 1 292 293
Concat                   cat_18                   3 1 268 270 293 294 0=0
Convolution              conv_77                  1 1 294 295 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_191                 1 1 295 296
Split                    splitncnn_33             1 4 296 297 298 299 300
Convolution              conv_78                  1 1 299 301 0=512 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2359296
Swish                    silu_192                 1 1 301 302
Concat                   cat_19                   2 1 302 189 303 0=0
Convolution              conv_79                  1 1 303 304 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=524288
Swish                    silu_193                 1 1 304 305
Slice                    split_9                  1 2 305 306 307 -23300=2,256,256 1=0
Split                    splitncnn_34             1 3 307 308 309 310
Convolution              conv_80                  1 1 310 311 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_194                 1 1 311 312
Split                    splitncnn_35             1 2 312 313 314
Convolution              conv_81                  1 1 314 315 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_195                 1 1 315 316
Convolution              conv_82                  1 1 316 317 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_196                 1 1 317 318
BinaryOp                 add_18                   2 1 313 318 319 0=0
Split                    splitncnn_36             1 2 319 320 321
Convolution              conv_83                  1 1 321 322 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_197                 1 1 322 323
Convolution              conv_84                  1 1 323 324 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_198                 1 1 324 325
BinaryOp                 add_19                   2 1 320 325 326 0=0
Convolution              conv_85                  1 1 309 327 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_199                 1 1 327 328
Concat                   cat_20                   2 1 326 328 329 0=0
Convolution              conv_86                  1 1 329 330 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_200                 1 1 330 331
Concat                   cat_21                   3 1 306 308 331 332 0=0
Convolution              conv_87                  1 1 332 333 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_201                 1 1 333 334
Split                    splitncnn_37             1 3 334 335 336 337
Convolution              conv_88                  1 1 260 338 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_202                 1 1 338 339
Convolution              conv_89                  1 1 339 340 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_203                 1 1 340 341
Convolution              conv_90                  1 1 341 342 0=51 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3264
Reshape                  view_229                 1 1 342 343 0=-1 1=51
Permute                  transpose_239            1 1 343 344 0=1
Convolution              conv_91                  1 1 298 345 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=294912
Swish                    silu_204                 1 1 345 346
Convolution              conv_92                  1 1 346 347 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_205                 1 1 347 348
Convolution              conv_93                  1 1 348 349 0=51 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3264
Reshape                  view_230                 1 1 349 350 0=-1 1=51
Permute                  transpose_240            1 1 350 351 0=1
Convolution              conv_94                  1 1 336 352 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=294912
Swish                    silu_206                 1 1 352 353
Convolution              conv_95                  1 1 353 354 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_207                 1 1 354 355
Convolution              conv_96                  1 1 355 356 0=51 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3264
Reshape                  view_231                 1 1 356 357 0=-1 1=51
Permute                  transpose_241            1 1 357 358 0=1
Concat                   cat_22                   3 1 344 351 358 out1 0=0
Convolution              conv_97                  1 1 259 360 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_208                 1 1 360 361
Convolution              conv_98                  1 1 361 362 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_209                 1 1 362 363
Convolution              conv_99                  1 1 363 364 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
ConvolutionDepthWise     convdw_246               1 1 262 365 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
Swish                    silu_210                 1 1 365 366
Convolution              conv_100                 1 1 366 367 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_211                 1 1 367 368
ConvolutionDepthWise     convdw_247               1 1 368 369 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
Swish                    silu_212                 1 1 369 370
Convolution              conv_101                 1 1 370 371 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_213                 1 1 371 372
Convolution              conv_102                 1 1 372 373 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=256
Concat                   cat_23                   2 1 364 373 374 0=0
Convolution              conv_103                 1 1 297 375 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=294912
Swish                    silu_214                 1 1 375 376
Convolution              conv_104                 1 1 376 377 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_215                 1 1 377 378
Convolution              conv_105                 1 1 378 379 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
ConvolutionDepthWise     convdw_248               1 1 300 380 0=512 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=4608 7=512
Swish                    silu_216                 1 1 380 381
Convolution              conv_106                 1 1 381 382 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Swish                    silu_217                 1 1 382 383
ConvolutionDepthWise     convdw_249               1 1 383 384 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
Swish                    silu_218                 1 1 384 385
Convolution              conv_107                 1 1 385 386 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_219                 1 1 386 387
Convolution              conv_108                 1 1 387 388 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=256
Concat                   cat_24                   2 1 379 388 389 0=0
Convolution              conv_109                 1 1 335 390 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=294912
Swish                    silu_220                 1 1 390 391
Convolution              conv_110                 1 1 391 392 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_221                 1 1 392 393
Convolution              conv_111                 1 1 393 394 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
ConvolutionDepthWise     convdw_250               1 1 337 395 0=512 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=4608 7=512
Swish                    silu_222                 1 1 395 396
Convolution              conv_112                 1 1 396 397 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Swish                    silu_223                 1 1 397 398
ConvolutionDepthWise     convdw_251               1 1 398 399 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
Swish                    silu_224                 1 1 399 400
Convolution              conv_113                 1 1 400 401 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_225                 1 1 401 402
Convolution              conv_114                 1 1 402 403 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=256
Concat                   cat_25                   2 1 394 403 404 0=0
Reshape                  view_232                 1 1 374 405 0=-1 1=65
Permute                  transpose_242            1 1 405 406 0=1
Reshape                  view_233                 1 1 389 407 0=-1 1=65
Permute                  transpose_243            1 1 407 408 0=1
Reshape                  view_234                 1 1 404 409 0=-1 1=65
Permute                  transpose_244            1 1 409 410 0=1
Concat                   cat_26                   3 1 406 408 410 out0 0=0
