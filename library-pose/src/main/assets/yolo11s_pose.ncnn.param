7767517
279 332
Input                    in0                      0 1 in0
Convolution              conv_1                   1 1 in0 1 0=32 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=864
Swish                    silu_93                  1 1 1 2
Convolution              conv_2                   1 1 2 3 0=64 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=18432
Swish                    silu_94                  1 1 3 4
Convolution              conv_3                   1 1 4 5 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
Swish                    silu_95                  1 1 5 6
Slice                    split_0                  1 2 6 7 8 -23300=2,32,32 1=0
Split                    splitncnn_0              1 3 8 9 10 11
Convolution              conv_4                   1 1 11 12 0=16 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=4608
Swish                    silu_96                  1 1 12 13
Convolution              conv_5                   1 1 13 14 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=4608
Swish                    silu_97                  1 1 14 15
BinaryOp                 add_0                    2 1 10 15 16 0=0
Concat                   cat_0                    3 1 7 9 16 17 0=0
Convolution              conv_6                   1 1 17 18 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=12288
Swish                    silu_98                  1 1 18 19
Convolution              conv_7                   1 1 19 20 0=128 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=147456
Swish                    silu_99                  1 1 20 21
Convolution              conv_8                   1 1 21 22 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_100                 1 1 22 23
Slice                    split_1                  1 2 23 24 25 -23300=2,64,64 1=0
Split                    splitncnn_1              1 3 25 26 27 28
Convolution              conv_9                   1 1 28 29 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=18432
Swish                    silu_101                 1 1 29 30
Convolution              conv_10                  1 1 30 31 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=18432
Swish                    silu_102                 1 1 31 32
BinaryOp                 add_1                    2 1 27 32 33 0=0
Concat                   cat_1                    3 1 24 26 33 34 0=0
Convolution              conv_11                  1 1 34 35 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=49152
Swish                    silu_103                 1 1 35 36
Split                    splitncnn_2              1 2 36 37 38
Convolution              conv_12                  1 1 38 39 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=589824
Swish                    silu_104                 1 1 39 40
Convolution              conv_13                  1 1 40 41 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_105                 1 1 41 42
Slice                    split_2                  1 2 42 43 44 -23300=2,128,128 1=0
Split                    splitncnn_3              1 3 44 45 46 47
Convolution              conv_14                  1 1 47 48 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Swish                    silu_106                 1 1 48 49
Split                    splitncnn_4              1 2 49 50 51
Convolution              conv_15                  1 1 51 52 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_107                 1 1 52 53
Convolution              conv_16                  1 1 53 54 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_108                 1 1 54 55
BinaryOp                 add_2                    2 1 50 55 56 0=0
Split                    splitncnn_5              1 2 56 57 58
Convolution              conv_17                  1 1 58 59 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_109                 1 1 59 60
Convolution              conv_18                  1 1 60 61 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_110                 1 1 61 62
BinaryOp                 add_3                    2 1 57 62 63 0=0
Convolution              conv_19                  1 1 46 64 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Swish                    silu_111                 1 1 64 65
Concat                   cat_2                    2 1 63 65 66 0=0
Convolution              conv_20                  1 1 66 67 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_112                 1 1 67 68
Concat                   cat_3                    3 1 43 45 68 69 0=0
Convolution              conv_21                  1 1 69 70 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=98304
Swish                    silu_113                 1 1 70 71
Split                    splitncnn_6              1 2 71 72 73
Convolution              conv_22                  1 1 73 74 0=512 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1179648
Swish                    silu_114                 1 1 74 75
Convolution              conv_23                  1 1 75 76 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_115                 1 1 76 77
Slice                    split_3                  1 2 77 78 79 -23300=2,256,256 1=0
Split                    splitncnn_7              1 3 79 80 81 82
Convolution              conv_24                  1 1 82 83 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_116                 1 1 83 84
Split                    splitncnn_8              1 2 84 85 86
Convolution              conv_25                  1 1 86 87 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_117                 1 1 87 88
Convolution              conv_26                  1 1 88 89 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_118                 1 1 89 90
BinaryOp                 add_4                    2 1 85 90 91 0=0
Split                    splitncnn_9              1 2 91 92 93
Convolution              conv_27                  1 1 93 94 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_119                 1 1 94 95
Convolution              conv_28                  1 1 95 96 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_120                 1 1 96 97
BinaryOp                 add_5                    2 1 92 97 98 0=0
Convolution              conv_29                  1 1 81 99 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_121                 1 1 99 100
Concat                   cat_4                    2 1 98 100 101 0=0
Convolution              conv_30                  1 1 101 102 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_122                 1 1 102 103
Concat                   cat_5                    3 1 78 80 103 104 0=0
Convolution              conv_31                  1 1 104 105 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_123                 1 1 105 106
Convolution              conv_32                  1 1 106 107 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Swish                    silu_124                 1 1 107 108
Split                    splitncnn_10             1 2 108 109 110
Pooling                  maxpool2d_90             1 1 110 111 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_11             1 2 111 112 113
Pooling                  maxpool2d_91             1 1 113 114 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_12             1 2 114 115 116
Pooling                  maxpool2d_92             1 1 116 117 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Concat                   cat_6                    4 1 109 112 115 117 118 0=0
Convolution              conv_33                  1 1 118 119 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=524288
Swish                    silu_125                 1 1 119 120
Convolution              conv_34                  1 1 120 121 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_126                 1 1 121 122
Slice                    split_4                  1 2 122 123 124 -23300=2,256,256 1=0
Split                    splitncnn_13             1 2 124 125 126
Convolution              conv_35                  1 1 126 127 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Split                    splitncnn_14             1 3 127 128 129 130
Reshape                  view_178                 1 1 130 131 0=-1 1=128 2=4
Slice                    split_5                  1 3 131 132 133 134 -23300=3,32,32,64 1=1
Split                    splitncnn_15             1 2 134 135 136
Permute                  transpose_187            1 1 132 137 0=1
MatMul                   matmul_185               2 1 137 133 138
BinaryOp                 mul_6                    1 1 138 139 0=2 1=1 2=1.767770e-01
Softmax                  softmax_0                1 1 139 140 0=2 1=1
MatMul                   matmultransb_0           2 1 136 140 141 0=1
Reshape                  Tensor.view_6            2 1 141 128 142 6="1w,1h,256"
Reshape                  Tensor.reshape_4         2 1 135 129 143 6="1w,1h,256"
ConvolutionDepthWise     convdw_195               1 1 143 144 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
BinaryOp                 add_7                    2 1 142 144 145 0=0
Convolution              conv_36                  1 1 145 146 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
BinaryOp                 add_8                    2 1 125 146 147 0=0
Split                    splitncnn_16             1 2 147 148 149
Convolution              conv_37                  1 1 149 150 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
Swish                    silu_127                 1 1 150 151
Convolution              conv_38                  1 1 151 152 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=131072
BinaryOp                 add_9                    2 1 148 152 153 0=0
Concat                   cat_7                    2 1 123 153 154 0=0
Convolution              conv_39                  1 1 154 155 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=262144
Swish                    silu_128                 1 1 155 156
Split                    splitncnn_17             1 2 156 157 158
Interp                   upsample_176             1 1 158 159 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_8                    2 1 159 72 160 0=0
Convolution              conv_40                  1 1 160 161 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=196608
Swish                    silu_129                 1 1 161 162
Slice                    split_6                  1 2 162 163 164 -23300=2,128,128 1=0
Split                    splitncnn_18             1 3 164 165 166 167
Convolution              conv_41                  1 1 167 168 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=73728
Swish                    silu_130                 1 1 168 169
Convolution              conv_42                  1 1 169 170 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=73728
Swish                    silu_131                 1 1 170 171
BinaryOp                 add_10                   2 1 166 171 172 0=0
Concat                   cat_9                    3 1 163 165 172 173 0=0
Convolution              conv_43                  1 1 173 174 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=98304
Swish                    silu_132                 1 1 174 175
Split                    splitncnn_19             1 2 175 176 177
Interp                   upsample_177             1 1 177 178 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_10                   2 1 178 37 179 0=0
Convolution              conv_44                  1 1 179 180 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_133                 1 1 180 181
Slice                    split_7                  1 2 181 182 183 -23300=2,64,64 1=0
Split                    splitncnn_20             1 3 183 184 185 186
Convolution              conv_45                  1 1 186 187 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=18432
Swish                    silu_134                 1 1 187 188
Convolution              conv_46                  1 1 188 189 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=18432
Swish                    silu_135                 1 1 189 190
BinaryOp                 add_11                   2 1 185 190 191 0=0
Concat                   cat_11                   3 1 182 184 191 192 0=0
Convolution              conv_47                  1 1 192 193 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=24576
Swish                    silu_136                 1 1 193 194
Split                    splitncnn_21             1 4 194 195 196 197 198
Convolution              conv_48                  1 1 197 199 0=128 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=147456
Swish                    silu_137                 1 1 199 200
Concat                   cat_12                   2 1 200 176 201 0=0
Convolution              conv_49                  1 1 201 202 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=98304
Swish                    silu_138                 1 1 202 203
Slice                    split_8                  1 2 203 204 205 -23300=2,128,128 1=0
Split                    splitncnn_22             1 3 205 206 207 208
Convolution              conv_50                  1 1 208 209 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=73728
Swish                    silu_139                 1 1 209 210
Convolution              conv_51                  1 1 210 211 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=73728
Swish                    silu_140                 1 1 211 212
BinaryOp                 add_12                   2 1 207 212 213 0=0
Concat                   cat_13                   3 1 204 206 213 214 0=0
Convolution              conv_52                  1 1 214 215 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=98304
Swish                    silu_141                 1 1 215 216
Split                    splitncnn_23             1 4 216 217 218 219 220
Convolution              conv_53                  1 1 219 221 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=589824
Swish                    silu_142                 1 1 221 222
Concat                   cat_14                   2 1 222 157 223 0=0
Convolution              conv_54                  1 1 223 224 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_143                 1 1 224 225
Slice                    split_9                  1 2 225 226 227 -23300=2,256,256 1=0
Split                    splitncnn_24             1 3 227 228 229 230
Convolution              conv_55                  1 1 230 231 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_144                 1 1 231 232
Split                    splitncnn_25             1 2 232 233 234
Convolution              conv_56                  1 1 234 235 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_145                 1 1 235 236
Convolution              conv_57                  1 1 236 237 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_146                 1 1 237 238
BinaryOp                 add_13                   2 1 233 238 239 0=0
Split                    splitncnn_26             1 2 239 240 241
Convolution              conv_58                  1 1 241 242 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_147                 1 1 242 243
Convolution              conv_59                  1 1 243 244 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_148                 1 1 244 245
BinaryOp                 add_14                   2 1 240 245 246 0=0
Convolution              conv_60                  1 1 229 247 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_149                 1 1 247 248
Concat                   cat_15                   2 1 246 248 249 0=0
Convolution              conv_61                  1 1 249 250 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_150                 1 1 250 251
Concat                   cat_16                   3 1 226 228 251 252 0=0
Convolution              conv_62                  1 1 252 253 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=393216
Swish                    silu_151                 1 1 253 254
Split                    splitncnn_27             1 3 254 255 256 257
Convolution              conv_63                  1 1 196 258 0=51 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=58752
Swish                    silu_152                 1 1 258 259
Convolution              conv_64                  1 1 259 260 0=51 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=23409
Swish                    silu_153                 1 1 260 261
Convolution              conv_65                  1 1 261 262 0=51 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2601
Reshape                  view_179                 1 1 262 263 0=-1 1=51
Permute                  transpose_189            1 1 263 264 0=1
Convolution              conv_66                  1 1 218 265 0=51 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=117504
Swish                    silu_154                 1 1 265 266
Convolution              conv_67                  1 1 266 267 0=51 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=23409
Swish                    silu_155                 1 1 267 268
Convolution              conv_68                  1 1 268 269 0=51 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2601
Reshape                  view_180                 1 1 269 270 0=-1 1=51
Permute                  transpose_190            1 1 270 271 0=1
Convolution              conv_69                  1 1 256 272 0=51 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=235008
Swish                    silu_156                 1 1 272 273
Convolution              conv_70                  1 1 273 274 0=51 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=23409
Swish                    silu_157                 1 1 274 275
Convolution              conv_71                  1 1 275 276 0=51 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2601
Reshape                  view_181                 1 1 276 277 0=-1 1=51
Permute                  transpose_191            1 1 277 278 0=1
Concat                   cat_17                   3 1 264 271 278 out1 0=0
Convolution              conv_72                  1 1 195 280 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=73728
Swish                    silu_158                 1 1 280 281
Convolution              conv_73                  1 1 281 282 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_159                 1 1 282 283
Convolution              conv_74                  1 1 283 284 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
ConvolutionDepthWise     convdw_196               1 1 198 285 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
Swish                    silu_160                 1 1 285 286
Convolution              conv_75                  1 1 286 287 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_161                 1 1 287 288
ConvolutionDepthWise     convdw_197               1 1 288 289 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
Swish                    silu_162                 1 1 289 290
Convolution              conv_76                  1 1 290 291 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_163                 1 1 291 292
Convolution              conv_77                  1 1 292 293 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=128
Concat                   cat_18                   2 1 284 293 294 0=0
Convolution              conv_78                  1 1 217 295 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=147456
Swish                    silu_164                 1 1 295 296
Convolution              conv_79                  1 1 296 297 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_165                 1 1 297 298
Convolution              conv_80                  1 1 298 299 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
ConvolutionDepthWise     convdw_198               1 1 220 300 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
Swish                    silu_166                 1 1 300 301
Convolution              conv_81                  1 1 301 302 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_167                 1 1 302 303
ConvolutionDepthWise     convdw_199               1 1 303 304 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
Swish                    silu_168                 1 1 304 305
Convolution              conv_82                  1 1 305 306 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_169                 1 1 306 307
Convolution              conv_83                  1 1 307 308 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=128
Concat                   cat_19                   2 1 299 308 309 0=0
Convolution              conv_84                  1 1 255 310 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=294912
Swish                    silu_170                 1 1 310 311
Convolution              conv_85                  1 1 311 312 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=36864
Swish                    silu_171                 1 1 312 313
Convolution              conv_86                  1 1 313 314 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
ConvolutionDepthWise     convdw_200               1 1 257 315 0=512 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=4608 7=512
Swish                    silu_172                 1 1 315 316
Convolution              conv_87                  1 1 316 317 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_173                 1 1 317 318
ConvolutionDepthWise     convdw_201               1 1 318 319 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
Swish                    silu_174                 1 1 319 320
Convolution              conv_88                  1 1 320 321 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_175                 1 1 321 322
Convolution              conv_89                  1 1 322 323 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=128
Concat                   cat_20                   2 1 314 323 324 0=0
Reshape                  view_182                 1 1 294 325 0=-1 1=65
Permute                  transpose_192            1 1 325 326 0=1
Reshape                  view_183                 1 1 309 327 0=-1 1=65
Permute                  transpose_193            1 1 327 328 0=1
Reshape                  view_184                 1 1 324 329 0=-1 1=65
Permute                  transpose_194            1 1 329 330 0=1
Concat                   cat_21                   3 1 326 328 330 out0 0=0
