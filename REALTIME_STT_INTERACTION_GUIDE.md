# 🎤 实时STT交互指南

## 🎯 **新的交互方式**

### **改进前（防抖延迟显示）**
```
用户说话 → STT识别 → 等待1.2秒防抖 → 显示完整文本 → 请求AI
```

### **改进后（实时显示）**
```
用户说话 → STT识别 → 立即显示 → 继续拼接 → 完整后请求AI
```

## 🔧 **技术实现**

### **核心流程**
```kotlin
1. handleSttTextWithDebounce(text)
   ├─ appendSttText(text)           // 拼接文本
   ├─ displayCurrentSttText()       // 立即显示
   └─ 启动防抖任务 → finalizeUserMessage() // 延迟请求AI

2. displayCurrentSttText()
   ├─ 第一次：创建新用户消息
   └─ 后续：更新现有用户消息

3. finalizeUserMessage()
   ├─ 确保用户消息完整显示
   ├─ 检查本地指令拦截
   └─ 请求AI回复
```

### **关键方法**

#### **1. 实时显示**
```kotlin
private fun displayCurrentSttText() {
    val currentText = sttTextBuilder.toString().trim()
    
    if (currentUserMessageIndex == -1) {
        // 创建新消息
        val userMessage = BasicChatAdapter.ChatMessage(
            type = 0,
            content = currentText,
            isMarkdown = false
        )
        chatAdapter.addMessage(userMessage)
        currentUserMessageIndex = chatAdapter.itemCount - 1
    } else {
        // 更新现有消息
        chatAdapter.updateMessage(currentUserMessageIndex, currentText)
    }
}
```

#### **2. 最终处理**
```kotlin
private fun finalizeUserMessage(completeText: String) {
    // 确保用户消息完整
    if (currentUserMessageIndex == -1) {
        // 创建消息
    } else {
        // 最终更新
        chatAdapter.updateMessage(currentUserMessageIndex, completeText)
    }
    
    // 检查本地指令
    val result = ToolsUtils.interceptFun(completeText)
    if (result != null) {
        // 本地处理
    } else {
        // 请求AI
        callSseExample(question = completeText)
    }
}
```

#### **3. 消息更新**
```kotlin
// 在 BasicChatAdapter 中新增
fun updateMessage(index: Int, content: String, isMarkdown: Boolean = false) {
    if (index >= 0 && index < chatMessages.size) {
        val message = chatMessages[index]
        chatMessages[index] = message.copy(
            content = content,
            isMarkdown = isMarkdown
        )
        notifyItemChanged(index)
    }
}
```

## 🎮 **用户体验**

### **实时反馈效果**
```
用户说："我想"
界面显示：我想

用户继续："开始"
界面更新：我想，开始

用户继续："跳绳训练"
界面更新：我想，开始，跳绳训练

1.2秒后：
- 检查本地指令（匹配"跳绳"相关）
- 执行跳绳启动功能
- 显示AI回复
```

### **视觉体验**
```
✅ 立即反馈：
- 用户说话时立即看到文字出现
- 文字逐步拼接，实时更新
- 给用户即时的反馈感

✅ 智能拼接：
- 自动添加标点符号
- 合理的文本分段
- 避免重复内容
```

## 📊 **性能优化**

### **状态管理**
```kotlin
// 跟踪当前用户消息
private var currentUserMessageIndex = -1

// 文本拼接缓存
private val sttTextBuilder = StringBuilder()

// 防抖任务管理
private var sttDebounceJob: Job? = null
```

### **内存优化**
```kotlin
// 及时清理状态
sttTextBuilder.clear()
lastSttText = ""
currentUserMessageIndex = -1
```

### **UI更新优化**
```kotlin
// 只更新必要的项目
notifyItemChanged(index)  // 而不是 notifyDataSetChanged()

// 智能滚动
BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
```

## 🔍 **调试信息**

### **日志输出**
```
STT 实时: 收到文本 '我想'，当前拼接: '我想'
STT 实时显示: 创建新消息 '我想'
STT 实时: 收到文本 '开始'，当前拼接: '我想，开始'
STT 实时更新: 更新消息 '我想，开始'
STT 实时: 收到文本 '跳绳训练'，当前拼接: '我想，开始，跳绳训练'
STT 实时更新: 更新消息 '我想，开始，跳绳训练'
STT 完成: 最终文本 '我想，开始，跳绳训练'
```

### **状态跟踪**
```
currentUserMessageIndex: -1 → 0 → 0 → 0 → -1
sttTextBuilder: "" → "我想" → "我想，开始" → "我想，开始，跳绳训练" → ""
```

## 🛠️ **配置参数**

### **防抖延迟**
```kotlin
private val sttDebounceDelayMs = 1200L // 1.2秒

// 可根据需要调整：
// 500L  - 快速响应，可能频繁请求
// 1000L - 平衡响应速度和稳定性
// 1500L - 更稳定，但响应稍慢
```

### **文本拼接规则**
```kotlin
// 自动添加标点符号
val needsSpace = !currentText.endsWith(" ") &&
                !currentText.endsWith("，") &&
                !currentText.endsWith("。") &&
                !currentText.endsWith("？") &&
                !currentText.endsWith("！")
if (needsSpace) {
    sttTextBuilder.append("，")
}
```

## ✅ **优势对比**

### **用户体验提升**
```
✅ 即时反馈：
- 说话立即看到文字
- 减少等待焦虑
- 提升交互流畅度

✅ 可视化进度：
- 看到文本逐步构建
- 了解识别进度
- 可以及时纠正
```

### **功能完整性**
```
✅ 保持原有功能：
- 防抖机制仍然有效
- 本地指令拦截正常
- AI请求逻辑不变

✅ 增强交互体验：
- 实时文本显示
- 智能文本拼接
- 流畅的视觉反馈
```

## 🎯 **最佳实践**

### **1. 合理的防抖时间**
- 太短：频繁请求AI，浪费资源
- 太长：用户等待时间过长
- 推荐：1000-1500ms

### **2. 智能文本处理**
- 自动添加标点符号
- 避免重复内容
- 保持文本可读性

### **3. 状态管理**
- 及时清理临时状态
- 避免内存泄漏
- 确保状态一致性

## 🔮 **未来扩展**

### **可能的改进**
```
1. 语音置信度显示
   - 显示识别置信度
   - 低置信度文字用不同颜色

2. 实时语音波形
   - 显示说话时的音频波形
   - 增强视觉反馈

3. 智能断句
   - 根据语音停顿自动断句
   - 更自然的文本分段

4. 撤销/重说功能
   - 允许用户撤销当前输入
   - 重新开始语音输入
```

这种实时交互方式大大提升了用户体验，让语音输入更加自然和流畅！
